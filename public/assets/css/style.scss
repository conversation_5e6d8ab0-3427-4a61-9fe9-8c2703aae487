/*
@File: Psylo Template Styles

* This file contains the styling for the actual template, this
is the file you need to edit to change the look of the
template.

This files table contents are outlined below>>>>>

*******************************************
*******************************************

** - Default CSS
** - Default btn CSS
** - Section Title CSS
** - Preloader Area CSS
** - Top Header Area CSS
** - Navbar Area CSS
** - Search Layout CSS
** - Main Banner Area CSS
** - Main Slides Area CSS
** - Features Area CSS
** - About Area CSS
** - Partner Area CSS
** - Offer Area CSS
** - Reviews Area CSS
** - Philosophy Area CSS
** - Products Area CSS
** - Blog Area CSS
** - Services Area CSS
** - FAQS Area CSS
** - Fun Facts Area CSS
** - Pricing Area CSS
** - Download Area CSS
** - Page Banner Area CSS
** - Coaches Area CSS
** - Coaches Details Area CSS
** - Events Area CSS
** - Events Details Area CSS
** - Success Story Area CSS
** - Courses Area CSS
** - Membership Levels Area CSS
** - Become Coaches Area CSS
** - Courses Details Area CSS
** - Services Details Area CSS
** - Cart Area CSS
** - Wishlist Area CSS
** - Checkout Area CSS
** - My Account Area CSS
** - Products Details Area CSS
** - Blog Details Area CSS
** - Purchase Guide Area CSS
** - Book Online Area CSS
** - Gallery Area CSS
** - Book Appointment Area CSS
** - 404 Error Area CSS
** - Privacy Policy Area CSS
** - Terms of Service Area CSS
** - Coming Soon Area CSS
** - Contact Info Area CSS
** - Contact Area CSS
** - Pagination Area CSS
** - Subscribe Area CSS
** - Footer Area CSS
** - Copy Right Area CSS
** - Go Top CSS

*/

/*================================================
Default CSS
=================================================*/
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@import url('https://fonts.googleapis.com/css2?family=Libre+Franklin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

$main-font-family: 'Libre Franklin', sans-serif;
$heading-font-family: 'Poppins', sans-serif;
$title-font-family: 'Open Sans', sans-serif;
$main-color: #000000;
$optional-color: #6c757d;
$white-color: #ffffff;
$black-color: #212529;
$paragraph-color: #6b6b84;
$font-size: 16px;
$transition: .6s;

body {
    padding: 0;
    margin: 0;
    font-family: $main-font-family;
    font-size: $font-size;
}
a {
    text-decoration: none;
    transition: $transition;
    color: $black-color;
    display: inline-block;

    &:hover, &:focus {
        color: $main-color;
        text-decoration: none;
    }
}
button {
    outline: 0 !important;
}
p {
    margin-bottom: 15px;
    line-height: 1.8;
    color: $paragraph-color;
    font-weight: 500;
    font-family: $main-font-family;
    font-size: $font-size;

    &:last-child {
        margin-bottom: 0;
    }
}
i {
    line-height: normal;
}
.d-table {
    width: 100%;
    height: 100%;

    &-cell {
        vertical-align: middle;
    }
}
img {
    max-width: 100%;
    height: auto;
    display: inline-block;
}
.ptb-100 {
    padding-top: 100px;
    padding-bottom: 100px;
}
.pt-100 {
    padding-top: 100px;
}
.pb-100 {
    padding-bottom: 100px;
}
.pb-70 {
    padding-bottom: 70px;
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
    color: $black-color;
    font-family: $title-font-family;
    font-weight: bold;

    &:last-child {
        margin-bottom: 0;
    }
}

// Specific heading styles
h1, .h1 {
    font-family: $title-font-family;
    font-weight: 700;
}

h2, .h2 {
    font-family: $title-font-family;
    font-weight: 600;
}

h3, .h3 {
    font-family: $heading-font-family;
    font-weight: 600;
}

h4, .h4, h5, .h5, h6, .h6 {
    font-family: $heading-font-family;
    font-weight: 500;
}

/*================================================
Default Btn CSS
=================================================*/
.default-btn {
    display: inline-block;
    padding: 15px 60px 15px 10px;
    border-radius: 10px 10px 0 10px;
    font-size: 15px;
    font-weight: 500;
    background-color: $main-color;
    color: $black-color;
    transition: $transition;
    position: relative;
    
    i {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
        display: inline-block;
        height: 38px;
        width: 38px;
        line-height: 38px;
        color: $white-color;
        border-radius: 10px 10px 0 10px;
        background-color: $black-color;
        transition: $transition;
    }
    &:hover {
        background: $optional-color;
        color: $white-color;

        i {
            background-color: $white-color;
            color: $black-color;
        }
    }
}
.optional-btn {
    display: inline-block;
    padding: 15px 60px 14px 15px;
    border-radius: 10px 10px 0 10px;
    font-size: 15px;
    font-weight: 500;
    background-color: $white-color;
    color: $black-color;
    transition: $transition;
    position: relative;
    
    i {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
        display: inline-block;
        height: 38px;
        width: 38px;
        line-height: 38px;
        color: $white-color;
        border-radius: 10px 10px 0 10px;
        background-color: $optional-color;
        transition: $transition;
    }
    &:hover {
        background: $main-color;
        color: $white-color;

        i {
            background-color: $white-color;
            color: $black-color;
        }
    }
}

/*================================================
Section Title CSS
=================================================*/
.section-title {
    text-align: center;
    margin-bottom: 45px;

    h2 {
        font-size: 45px;
        margin-bottom: 20px;

        span {
            font-weight: 400;
            font-style: italic;
        }
    }
    p {
        max-width: 650px;
        margin: auto;
    }
}

/*================================================
Preloader Area CSS
=================================================*/
.preloader-area {
    position: fixed;
    top: 0;
    background-color: $main-color;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    text-align: center;

    .spinner {
        width: 4em;
        height: 4em;
        transform: perspective(20em) rotateX(-24deg) rotateY(20deg) rotateZ(30deg);
        transform-style: preserve-3d;
        position: absolute;
        left: 0;
        right: 0;
        top: 45%;
        transform: translateY(-45%);
        margin-left: auto;
        margin-right: auto;

        .disc {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            border: 0.3em dotted $white-color;

            &:nth-child(1) {
                animation: rotate 12s linear infinite;
            }
            &:nth-child(2) {
                animation: rotateDisc2 12s linear infinite;
            }
            &:nth-child(3) {
                animation: rotateDisc3 12s linear infinite;
            }
        }
        .inner {
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            animation: sphereSpin 6s linear infinite;
        }
        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            border-radius: 50%;
            right: 0;
            bottom: 0;
            border: 2px dotted $white-color;
            margin: -15px;
        }
    }
}
@keyframes sphereSpin {
    0% {
        transform: rotateX(360deg) rotateY(0deg);
    }
    100% {
        transform: rotateX(0deg) rotateY(360deg);
    }
}
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
@keyframes rotateDisc2 {
    from {
        transform: rotateX(90deg) rotateZ(0deg);
    }
    to {
        transform: rotateX(90deg) rotateZ(360deg);
    }
}
@keyframes rotateDisc3 {
    from {
        transform: rotateY(90deg) rotateZ(0deg);
    }
    to {
        transform: rotateY(90deg) rotateZ(360deg);
    }
}

/*================================================
Top Header Area CSS
=================================================*/
.top-header-area {
    background-color: $black-color;
    padding-top: 15px;
    padding-bottom: 15px;

    &.bg-transparent {
        border-bottom: 1px solid #eeeeee;

        .top-header-information {
            li {
                color: $optional-color;

                a {
                    color: $optional-color;

                    &:hover {
                        color: $main-color;
                    }
                }
                i {
                    color: $optional-color;
                }
            }
        }
        .top-header-optional {
            li {
                a {
                    &::before {
                        background-color: #221d485e;
                    }
                    i {
                        color: $white-color;
        
                        &:hover {
                            transform: translateY(-2px);
                            color: $main-color;
                        }
                    }
                }
            }
        }
    }
    &.bg-color {
        background-color: $main-color;

        .top-header-information {
            li {
                color: $optional-color;

                a {
                    color: $optional-color;

                    &:hover {
                        color: $white-color;
                    }
                }
                i {
                    color: $optional-color;
                }
            }
        }
        .top-header-optional {
            li {
                a {
                    &::before {
                        background-color: #221d485e;
                    }
                    i {
                        color: $optional-color;
        
                        &:hover {
                            transform: translateY(-2px);
                            color: $white-color;
                        }
                    }
                }
            }
        }
    }
}
.top-header-information {
    padding-left: 0;
    margin-bottom: 0;

    li {
        color: $white-color;
        font-size: 15px;
        font-weight: 500;
        list-style-type: none;
        display: inline-block;
        position: relative;
        margin-right: 15px;
        padding-left: 22px;

        &:last-child {
            margin-right: 0;
        }
        a {
            color: $white-color;

            &:hover {
                color: $main-color;
            }
        }
        i {
            position: absolute;
            left: 0;
            top: 3px;
            color: $main-color;
        }
    }
}
.top-header-optional {
    padding-left: 0;
    margin-bottom: 0;
    text-align: right;

    li {
        list-style-type: none;
        display: inline-block;

        a {
            margin-right: 12px;
            position: relative;

            &::before {
                position: absolute;
                right: -8px;
                top: 3px;
                width: 1px;
                content: '';
                height: 18px;
                background-color: #dadada59;
            }
            &:last-child {
                margin-right: 0;

                &::before {
                    display: none;
                }
            }
            i {
                color: $white-color;
                font-size: 20px;
                transition: $transition;

                &:hover {
                    transform: translateY(-2px);
                    color: $main-color;
                }
            }
        }
    }
}
.header-information {
    display: none;
    background-color: $main-color;
    color: $white-color;
    cursor: pointer;
    padding: 15px;
    text-align: center;
    font-size: $font-size;
    font-weight: 500;
}

/*================================================
Navbar Area CSS
=================================================*/
.main-responsive-nav {
    display: none;
}
.main-navbar {
    padding: 10px 0;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    
    .navbar {
        transition: $transition;
        padding: 0;

        .navbar-brand {
            padding: 0;
        }
        ul {
            padding-left: 0;
            list-style-type: none;
            margin-bottom: 0;
        }
        .navbar-nav {
            .nav-item {
                position: relative;
                margin-left: 15px;
                margin-right: 15px;
                
                a {
                    color: $black-color;
                    font-size: $font-size;
                    font-weight: 600;
                    padding-left: 0;
                    padding-right: 0;
                    padding-top: 25px;
                    padding-bottom: 20px;
                    transition: $transition;

                    i {
                        font-size: 15px;
                        position: relative;
                        top: 2px;
                        display: inline-block;
                        margin-left: -5px;
                        margin-right: -5px;
                    }
                    &:hover, &:focus, &.active {
                        color: $main-color;
                    }
                }
                &:last-child {
                    margin-right: 0;
                }
                &:first-child {
                    margin-left: 0;
                }
                &:hover, &.active {
                    a {
                        color: $main-color;
                    }
                }
                .dropdown-menu {
                    border: none;
                    top: 70px;
                    left: 0;
                    z-index: 99;
                    opacity: 0;
                    width: 250px;
                    display: block;
                    border-radius: 0;
                    padding: 10px 0;
                    position: absolute;
                    visibility: hidden;
                    opacity: 0;
                    margin-top: 10px;
                    background: $white-color;
                    transition: $transition;
                    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);

                    li {
                        margin: 0;

                        a {
                            padding: 8px 20px;
                            position: relative;
                            display: block;
                            color: $black-color;
                            font-size: 15px;
                            font-weight: 600;
                            transition: $transition;

                            i {
                                margin: 0;
                                position: absolute;
                                top: 50%;
                                font-size: 15px;
                                transform: translateY(-50%);
                                right: 15px;
                            }
                            &:hover, &:focus, &.active {
                                color: $main-color;
                                letter-spacing: 1px;
                            }
                        }
                        .dropdown-menu {
                            top: 0;
                            opacity: 0;
                            right: -250px;
                            left: auto;
                            visibility: hidden;
                            transform: translateY(5px);
                            
                            li {
                                a {
                                    color: $black-color;
                                    
                                    &:hover, &:focus, &.active {
                                        color: $main-color;
                                    }
                                }
                                .dropdown-menu {
                                    top: 0;
                                    opacity: 0;
                                    left: 250px;
                                    visibility: hidden;
        
                                    li {
                                        a {
                                            color: $black-color;
        
                                            &:hover, &:focus, &.active {
                                                color: $main-color;
                                            }
                                        }
                                        .dropdown-menu {
                                            top: 0;
                                            opacity: 0;
                                            left: -250px;
                                            visibility: hidden;
                
                                            li {
                                                a {
                                                    color: $black-color;
                
                                                    &:hover, &:focus, &.active {
                                                        color: $main-color;
                                                    }
                                                }
                                                .dropdown-menu {
                                                    top: 0;
                                                    opacity: 0;
                                                    left: 250px;
                                                    visibility: hidden;
                        
                                                    li {
                                                        a {
                                                            color: $black-color;
                
                                                            &:hover, &:focus, &.active {
                                                                color: $main-color;
                                                            }
                                                        }
                                                        .dropdown-menu {
                                                            top: 0;
                                                            opacity: 0;
                                                            left: -250px;
                                                            visibility: hidden;
                                
                                                            li {
                                                                a {
                                                                    color: $black-color;
                
                                                                    &:hover, &:focus, &.active {
                                                                        color: $main-color;
                                                                    }
                                                                }
                                                                .dropdown-menu {
                                                                    top: 0;
                                                                    opacity: 0;
                                                                    left: 250px;
                                                                    visibility: hidden;
                                        
                                                                    li {
                                                                        a {
                                                                            color: $black-color;
                
                                                                            &:hover, &:focus, &.active {
                                                                                color: $main-color;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                &.active {
                                                                    a {
                                                                        color: $main-color;
                                                                    }
                                                                }
                                                                &:hover {
                                                                    .dropdown-menu {
                                                                        opacity: 1;
                                                                        visibility: visible;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        &.active {
                                                            a {
                                                                color: $main-color;
                                                            }
                                                        }
                                                        &:hover {
                                                            .dropdown-menu {
                                                                opacity: 1;
                                                                visibility: visible;
                                                            }
                                                        }
                                                    }
                                                }
                                                &.active {
                                                    a {
                                                        color: $main-color;
                                                    }
                                                }
                                                &:hover {
                                                    .dropdown-menu {
                                                        opacity: 1;
                                                        visibility: visible;
                                                    }
                                                }
                                            }
                                        }
                                        &.active {
                                            a {
                                                color: $main-color;
                                            }
                                        }
                                        &:hover {
                                            .dropdown-menu {
                                                opacity: 1;
                                                visibility: visible;
                                            }
                                        }
                                    }
                                }
                                &.active {
                                    a {
                                        color: $main-color;
                                    }
                                }
                                &:hover {
                                    .dropdown-menu {
                                        opacity: 1;
                                        visibility: visible;
                                    }
                                }
                            }
                        }
                        &.active {
                            a {
                                color: $main-color;
                            }
                        }
                        &:hover {
                            .dropdown-menu {
                                opacity: 1;
                                visibility: visible;
                                transform: translateY(0);
                            }
                        }
                    }
                }
                &:hover {
                    .dropdown-menu {
                        opacity: 1;
                        visibility: visible;
                        margin-top: 0;
                    }
                }
            }
        }
        .others-options {
            .option-item {
                margin-right: 25px;

                &:last-child {
                    margin-right: 0;
                }
                .cart-btn {
                    position: relative;
                    top: 5px;

                    a {
                        font-size: 25px;
                        color: $black-color;
                        transition: $transition;
                        position: relative;
                        
                        &:hover {
                            color: $main-color;
                        }
                        span {
                            position: absolute;
                            right: -5px;
                            top: 0;
                            width: 15px;
                            height: 15px;
                            text-align: center;
                            line-height: 15px;
                            border-radius: 50%;
                            background: $black-color;
                            color: $white-color;
                            font-size: 10px;
                        }
                    }
                }
                .search-box {
                    position: relative;
                    top: 2px;

                    i {
                        display: inline-block;
                        font-size: 25px;
                        color: $black-color;
                        transition: $transition;
                        cursor: pointer;

                        &:hover {
                            color: $main-color;
                        }
                    }
                }
                .navbar-btn {
                    .default-btn {
                        display: inline-block;
                        padding: 15px 60px 15px 15px;
                        border-radius: 10px 10px 0 10px;
                        font-size: $font-size;
                        font-weight: 600;
                        background-color: $black-color;
                        color: $white-color;
                        transition: $transition;
                        position: relative;
                        
                        i {
                            position: absolute;
                            right: 10px;
                            top: 50%;
                            transform: translateY(-50%);
                            text-align: center;
                            display: inline-block;
                            height: 38px;
                            width: 38px;
                            line-height: 38px;
                            color: $white-color;
                            border-radius: 10px 10px 0 10px;
                            background-color: $main-color;
                            transition: $transition;
                        }
                        &:hover {
                            background: $main-color;
                            color: $black-color;
                    
                            i {
                                background-color: $white-color;
                                color: $black-color;
                            }
                        }
                    }
                }
            }
        }
    }
}
.p-relative-color {
    .main-navbar {
        .navbar {
            .others-options {
                .option-item {
                    .cart-btn {
                        a {
                            color: $white-color;

                            &:hover {
                                color: $main-color;
                            }
                            span {
                                background: $white-color;
                                color: $main-color;
                            }
                        }
                    }
                    .search-box {
                        i {
                            color: $white-color;

                            &:hover {
                                color: $main-color;
                            }
                        }
                    }
                }
            }
        }
    }
}
.navbar-two {
    .main-navbar {
        .navbar {
            .others-options {
                margin-left: 30px;
            }
        }
    }
}
.navbar-area {
    &.is-sticky {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 999;
        background: $white-color;
        box-shadow: 0 0 1.25rem rgba(108, 118, 134, .1);
        animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
        transition: $transition;

        &.p-relative-color {
            .main-navbar {
                .navbar {
                    .others-options {
                        .option-item {
                            .cart-btn {
                                a {
                                    color: $black-color;
        
                                    &:hover {
                                        color: $main-color;
                                    }
                                    span {
                                        background: $black-color;
                                        color: $main-color;
                                    }
                                }
                            }
                            .search-box {
                                i {
                                    color: $black-color;
        
                                    &:hover {
                                        color: $main-color;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
.others-option-for-responsive {
    display: none;

    .dot-menu {
        padding: 0 10px;
        height: 30px;
        cursor: pointer;
        z-index: 9991;
        position: absolute;
        right: 60px;
        top: -28px;

        .inner {
            display: flex;
            align-items: center;
            height: 30px;

            .circle {
                height: 5px;
                width: 5px;
                border-radius: 100%;
                margin: 0 2px;
                transition: $transition;
                background-color: $black-color;
            }
        }
        &:hover {
            .inner {
                .circle {
                    background-color: $main-color;
                }
            }
        }
    }
    .container {
        position: relative;

        .container {
            position: absolute;
            right: 0;
            top: 10px;
            max-width: 260px;
            background-color: $white-color;
            box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
            margin-left: auto;
            opacity: 0;
            visibility: hidden;
            transition: $transition;
            transform: scaleX(0);
            z-index: 2;
            padding: {
                left: 15px;
                right: 15px;
            };
            &.active {
                opacity: 1;
                visibility: visible;
                transform: scaleX(1);
            }
        }
    }
    .option-inner {
        .others-options {
            .option-item {
                margin-right: 25px;

                &:last-child {
                    margin-right: 0;
                }
                .cart-btn {
                    position: relative;
                    top: 5px;

                    a {
                        font-size: 25px;
                        color: $black-color;
                        transition: $transition;
                        position: relative;
                        
                        &:hover {
                            color: $main-color;
                        }
                        span {
                            position: absolute;
                            right: -5px;
                            top: 0;
                            width: 15px;
                            height: 15px;
                            text-align: center;
                            line-height: 15px;
                            border-radius: 50%;
                            background: $black-color;
                            color: $white-color;
                            font-size: 10px;
                        }
                    }
                }
                .search-box {
                    position: relative;
                    top: 2px;

                    i {
                        display: inline-block;
                        font-size: 25px;
                        color: $black-color;
                        transition: $transition;
                        cursor: pointer;

                        &:hover {
                            color: $main-color;
                        }
                    }
                }
                .navbar-btn {
                    .default-btn {
                        display: inline-block;
                        padding: 15px 60px 15px 15px;
                        border-radius: 10px 10px 0 10px;
                        font-size: $font-size;
                        font-weight: 600;
                        background-color: $black-color;
                        color: $white-color;
                        transition: $transition;
                        position: relative;
                        
                        i {
                            position: absolute;
                            right: 10px;
                            top: 50%;
                            transform: translateY(-50%);
                            text-align: center;
                            display: inline-block;
                            height: 38px;
                            width: 38px;
                            line-height: 38px;
                            color: $white-color;
                            border-radius: 10px 10px 0 10px;
                            background-color: $main-color;
                            transition: $transition;
                        }
                        &:hover {
                            background: $main-color;
                            color: $black-color;
                    
                            i {
                                background-color: $white-color;
                                color: $black-color;
                            }
                        }
                    }
                }
            }
        }
    }
}
.main-header-area {
    position: absolute;
    width: 100%;
    z-index: 9;
}

/*================================================
Search Layout CSS
=================================================*/
.search-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    overflow: hidden;

    .search-overlay-layer {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        transform: translateX(100%);

        &:nth-child(1) {
            left: 0;
            background-color: rgba(0, 0, 0, 0.5);
            transition: all 0.3s ease-in-out 0s;
        }
        &:nth-child(2) {
            left: 0;
            background-color: rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease-in-out 0.3s;
        }
        &:nth-child(3) {
            left: 0;
            background-color: rgba(0, 0, 0, 0.7);
            transition: all 0.9s ease-in-out 0.6s;
        }
    }
    .search-overlay-close {
        position: absolute;
        top: 40px;
        right: 40px;
        width: 50px;
        z-index: 2;
        text-align: center;
        cursor: pointer;
        padding: 10px;
        transition: all 0.9s ease-in-out 1.5s;
        opacity: 0;
        visibility: hidden;

        .search-overlay-close-line {
            width: 100%;
            height: 3px;
            float: left;
            margin-bottom: 5px;
            background-color: $white-color;
            transition: all 500ms ease;

            &:nth-child(1) {
                transform: rotate(45deg);
            }
            &:nth-child(2) {
                margin-top: -7px;
                transform: rotate(-45deg);
            }
        }
        &:hover {
            .search-overlay-close-line {
                background: $main-color;
                transform: rotate(180deg);
            }
        }
    }
    .search-overlay-form {
        transition: all 0.9s ease-in-out 1.4s;
        opacity: 0;
        visibility: hidden;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateY(-50%) translateX(-50%);
        z-index: 2;
        max-width: 500px;
        width: 500px;

        form {
            position: relative;

            .input-search {
                display: block;
                width: 100%;
                height: 55px;
                border: none;
                border-radius: 30px;
                padding-left: 20px;
                color: $black-color;
                font-size: $font-size;
                outline: 0;
                transition: $transition;

                &::placeholder {
                    transition: $transition;
                    columns: $paragraph-color;
                }
                &:focus {
                    &::placeholder {
                        color: transparent;
                    }
                }
            }
            button {
                position: absolute;
                right: 5px;
                top: 5px;
                width: 45px;
                color: $white-color;
                height: 45px;
                border-radius: 50%;
                background-color: $main-color;
                transition: $transition;
                border: none;
                font-size: 20px;
                line-height: 45px;
                cursor: pointer;

                &:hover {
                    background-color: $black-color;
                    color: $white-color;
                }
            }
        }
    }
    &.search-overlay-active {
        &.search-overlay {
            opacity: 1;
            visibility: visible;
    
            .search-overlay-layer {
                transform: translateX(0);
            }
            .search-overlay-close {
                opacity: 1;
                visibility: visible;
            }
            .search-overlay-form {
                opacity: 1;
                visibility: visible;
            }
        }
    }
}

// Mobile and iPad Navbar
@media only screen and (max-width: 1199px) {
    .main-responsive-nav {
        display: block; 

        .main-responsive-menu {
            position: relative;
            
            &.mean-container {
                .mean-nav {
                    ul {
                        font-size: 15px;

                        li {
                            a {
                                &.active {
                                    color: $main-color;
                                }
                                i {
                                    display: none;
                                }
                            }
                            li {
                                a {
                                    font-size: 14px;
                                }
                            }
                        }
                    }
                }
                .others-options {
                    display: none !important;
                }
                .navbar-nav {
                    overflow-y: scroll;
                    height: 300px;
                    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, .1);
                }
            }
        }
        .mean-container {
            a {
                &.meanmenu-reveal {
                    color: $black-color;

                    span {
                        background: $black-color;
                        position: relative;
                    }
                }
            }
        }
        .logo {
            position: relative;
            img {
                max-width: 100px !important;
            }
        }
    }
    .navbar-area {
        background-color: $white-color;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .main-navbar {
        display: none;
    }
    .others-option-for-responsive {
        display: block;
    }
    .header-information {
        display: block;
    }
    .top-header-area {
        display: none;

        &.active {
            display: block;
        }
    }
}

/*================================================
Main Banner Area CSS
=================================================*/
.main-banner-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
    &.main-banner-video-area {
        .background-video {
            position: absolute;
            left: 0;
            top: 0;
            width: auto;
            height: auto;
            min-height: 100%;
            min-width: 100%;
            z-index: -2;
        }
        .main-banner-item {
            background-image: none;
            position: relative;
            overflow: hidden;
            z-index: 3;
        }
    }
}
.main-banner-item {
    background-image: url(../../assets/images/main-banner/banner-bg-1.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    padding-top: 150px;
    padding-bottom: 270px;
}
.main-banner-content {
    max-width: 825px;

    h1 {
        font-size: 75px;
        color: $white-color;
        margin-bottom: 15px;
        line-height: 1.4;
        font-weight: bold;

        span {
            font-weight: 400;
            font-style: italic;
        }
    }
    p {
        color: $white-color;
    }
    .banner-btn {
        margin-top: 38px;
        
        .default-btn {
            margin-right: 20px;
        }
    }
}
.main-banner-item-box {
    .container-fluid {
        padding-left: 0;
        overflow: hidden;
    }
    .main-banner-content {
        max-width: 715px;
        padding-left: 50px;
    
        h1 {
            font-size: 65px;
            color: $black-color;
            margin-bottom: 25px;
            line-height: 1.4;
            font-weight: bold;
    
            span {
                font-weight: 400;
            }
        }
        p {
            color: $paragraph-color;
        }
        .banner-btn {
            margin-top: 38px;
            
            .default-btn {
                margin-right: 20px;
            }
            .optional-btn {
                background-color: $optional-color;
                color: $white-color;

                i {
                    background-color: $main-color;
                }
                &:hover {
                    background-color: $main-color;

                    i {
                        background-color: $optional-color;
                        color: $white-color;
                    }
                }
            }
        }
    }
}
.main-banner-image {
    position: relative;

    .image-shape {
        position: absolute;
        top: 0;
        right: -100px;
        animation: moveleftbounce 8s linear infinite;
        max-width: 410px;
    }
}
.main-banner-shape {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;
    opacity: 30%;

    img {
        width: 100%;
    }
}
.main-banner-shape-two {
    position: absolute;
    top: 0;
    right: -10px;
    z-index: -1;
    animation: moveleftbounce 10s linear infinite;
}

/*================================================
Main Slides Area CSS
=================================================*/
.main-slides-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.main-slides-item {
    background-image: url(../../assets/images/main-slides/slides-bg-1.jpg);
    background-position: center bottom;
    background-size: cover;
    background-repeat: no-repeat;
    padding-top: 350px;
    padding-bottom: 350px;

    &.item-two {
        background-image: url(../../assets/images/main-slides/slides-bg-2.jpg);
    }
    &.item-three {
        background-image: url(../../assets/images/main-slides/slides-bg-3.jpg);
    }
}
.main-slides-item-box {
    position: relative;
    overflow: hidden;
    z-index: 1;

    .container-fluid {
        padding-right: 0;
    }
    .main-slides-content {
        max-width: 835px;
        margin-left: auto;
    }
}
.main-slides-content {
    max-width: 825px;

    h1 {
        font-size: 65px;
        margin-bottom: 15px;
        line-height: 1.4;
        color: $optional-color;
        font-weight: bold;
        animation-delay: 0.5s;

        span {
            font-weight: 400;
            font-style: italic;
        }
    }
    p {
        animation-delay: 1.8s;
    }
    .slides-btn {
        margin-top: 38px;
        
        .default-btn {
            margin-right: 20px;
            animation-delay: 2s;
        }
        .optional-btn {
            background-color: $optional-color;
            color: $white-color;
            animation-delay: 2.5s;

            i {
                background-color: $main-color;
            }
            &:hover {
                background-color: $main-color;

                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}
.home-slides {
    &.owl-theme {
        .owl-dots {
            margin-top: 0;
            position: absolute;
            bottom: 50px;
            left: 0;
            right: 0;

            .owl-dot {
                transition: $transition;

                span {
                    width: 5px;
                    height: 35px;
                    margin: 0 8px;
                    background: $main-color;
                    transition: $transition;
                    border-radius: 30px;
                }
                &:hover, &.active {
                    span {
                        background-color: $black-color;
                        height: 55px;

                        &::before {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }
        }
    }
}

/*================================================
Features Area CSS
=================================================*/
.features-area {
    position: relative;
    margin-top: -100px;
    z-index: 1;

    &.bg-ffffff {
        margin-top: 0;
    }
}
.single-features {
    background-color: $main-color;
    padding: 35px 30px 35px 30px;
    border-radius: 30px 30px 0 30px;
    transition: $transition;
    margin-bottom: 30px;
    
    .features-title {
        position: relative;
        padding-left: 85px;
        margin-bottom: 20px;
        transition: $transition;

        i {
            display: inline-block;
            height: 70px;
            width: 70px;
            line-height: 70px;
            background-color: $white-color;
            color: $black-color;
            text-align: center;
            font-size: 35px;
            border-radius: 20px 0 0 0;
            transition: $transition;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }
        h3 {
            font-size: 22px;
            margin-bottom: 12px;
            font-weight: 600;

            a {
                color: $black-color;
            }
        }
        span {
            font-size: 14px;
            font-weight: 500;
            color: $optional-color;
        }
    }
    p {
        font-weight: 500;
        color: $black-color;
        font-size: 15px;
    }
    &:hover {
        transform: translateY(-10px);

        // .features-title {
        //     background-color: $white-color;
        //     padding-top: 10px;
        //     padding-bottom: 10px;
        //     border-radius: 30px 30px 0 0;

        //     i {
        //         background-color: transparent;
        //         color: $main-color;
        //         border-radius: 50%;
        //         left: 10px;
        //     }
        // }
    }
}

/*================================================
About Area CSS
=================================================*/
.about-area {
    position: relative;
    z-index: 1;
    overflow: hidden;

    &.bg-ffffff {
        .about-main-content {
            padding-left: 0;
        }
    }
}
.about-main-image {
    position: relative;

    img {
        border-radius: 0 50px 0 0;
    }
    .about-shape {
        .shape-1 {
            position: absolute;
            left: -40px;
            top: 0;
            animation: moveleftbounce 5s linear infinite;
        }
        .shape-2 {
            position: absolute;
            left: -50px;
            top: 30%;
            transform: translateY(-30%);
            max-width: 100px;
            z-index: -1;
            animation: movebounce 5s linear infinite;
        }
        .shape-3 {
            position: absolute;
            bottom: 150px;
            left: -30px;
            animation: moveleftbounce 5s linear infinite;
        }
        .shape-4 {
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            animation: movebounce 5s linear infinite;
        }
        .shape-5 {
            position: absolute;
            bottom: 0;
            left: -10px;
        }
        &.about-wrap {
            .shape-2 {
                right: -50px;
                left: auto;
                top: 0;
                transform: unset;
                max-width: 150px;
            }
            .shape-3 {
                bottom: 105px;
            }
        }
    }
}
@keyframes movescale {
    0% {
        transform: scale(.8);
    }
    50% {
        transform: scale(1);
    }
    100% {
        transform: scale(.8);
    } 
}
@keyframes movebounce {
    0% {
        transform: translateY(0px); 
    }
    50% {
        transform: translateY(20px); 
    }
    100% {
        transform: translateY(0px); 
    } 
}
@keyframes moveleftbounce {
    0% {
        transform: translateX(0px); 
    }
    50% {
        transform: translateX(20px); 
    }
    100% {
        transform: translateX(0px); 
    } 
}
.about-main-content {
    padding-left: 35px;

    h3 {
        font-size: 35px;
        margin-bottom: 30px;
    }
    .about-content-image {
        position: relative;
        margin-bottom: 20px;

        .sub-title {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 35px;
            font-size: 25px;
            font-style: italic;
            margin-bottom: 0;
        }
        .video-btn {
            display: inline-block;
            width: 70px;
            height: 70px;
            line-height: 70px;
            background-color: $white-color;
            border-radius: 50%;
            color: $black-color;
            position: absolute;
            z-index: 1;
            top: 50%;
            transform: translateY(-50%);
            right: 35px;
            text-align: center;
            transition: $transition;

            i {
                font-size: 35px;
                position: relative;
                top: 8px;
                left: 5px;
                color: $optional-color;
                transition: $transition;
            }
            &::before {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                z-index: -1;
                bottom: 0;
                left: 0;
                border-radius: 50%;
                border: 2px solid $white-color;
                animation: ripple 1s linear infinite;
            }
            &:hover {
                background-color: $optional-color;
                
                i {
                    color: $white-color;
                }
            }
        }
    }
    p {
        margin-bottom: 10px;
    }
    b {
        font-weight: 400;
    }
    .about-information {
        position: relative;
        background-color: $white-color;
        box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
        padding: 20px 20px 20px 110px;
        border-radius: 20px 20px 0 20px;
        margin-top: 25px;

        i {
            display: inline-block;
            height: 70px;
            width: 70px;
            line-height: 70px;
            background-color: $main-color;
            color: $white-color;
            text-align: center;
            font-size: 35px;
            border-radius: 10px;
            transition: $transition;
            position: absolute;
            left: 25px;
            top: 16px;
        }
        h5 {
            font-size: 25px;
            font-family: $main-font-family;
            margin-bottom: 8px;
        }
        span {
            font-weight: 500;
            color: $optional-color;
        }
    }
    .about-btn {
        margin-top: 30px;

        .default-btn {
            background-color: $optional-color;
            color: $white-color;

            i {
                background-color: $white-color;
                color: $optional-color;
            }
            &:hover {
                background-color: $main-color;

                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}
@keyframes ripple {
    0% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.75);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}
.about-main-shape {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: -1;
    opacity: 70%;
}

/*================================================
Partner Area CSS
=================================================*/
.partner-area {
    background-color: $main-color;

    &.bg-transparent {
        background-color: transparent;
        position: absolute;
        bottom: 30px;
        right: 0;
        left: 0;

        .single-partner {
            background-color: $main-color;
        }
    }
    &.bg-color {
        background-color: $white-color;

        .single-partner {
            background-color: $main-color;
        }
    }
}
.single-partner {
    padding: 35px;
    background-color: $white-color;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    text-align: center;
    transition: $transition;
    border-radius: 30px 30px 0 30px;
    margin-top: 30px;
    margin-bottom: 30px;
    margin-left: 10px;
    margin-right: 10px;

    &:hover {
        transform: unset;
    }
    &.style-two {
        border-radius: 0 30px 30px 30px;
    }
}

/*================================================
Offer Area CSS
=================================================*/
.offer-area {
    background-image: url(../../assets/images/offer/offer-bg.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    z-index: 1;
    overflow: hidden;
    background-attachment: fixed;

    &::before {
        position: absolute;
        content: "";
        left: 0;
        right: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background-color: $black-color;
        z-index: -1;
        opacity: .70;
    }
    .container-fluid {
        padding-right: 0;
    }
}
.offer-item {
    padding-top: 45px;
    padding-bottom: 55px;
    max-width: 685px;
    margin-left: auto;

    .content {
        margin-bottom: 30px;

        h2 {
            font-size: 45px;
            margin-bottom: 0;
            color: $white-color;
        }
    }
    .accordion {
        .accordion-item {
            position: relative;
            background-color: $white-color;
            box-shadow: 0 0 1.25rem rgba(147, 153, 161, 0.1);
            padding: 15px 15px 15px 90px;
            border-radius: 30px 30px 0 30px;
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 0;
            }
            .accordion-title {
                cursor: pointer;

                i {
                    display: inline-block;
                    height: 60px;
                    width: 60px;
                    line-height: 60px;
                    background-color: $main-color;
                    color: $white-color;
                    text-align: center;
                    font-size: 30px;
                    border-radius: 10px;
                    transition: $transition;
                    position: absolute;
                    left: 15px;
                    top: 12px;
                }
                h3 {
                    font-size: 22px;
                    font-family: $main-font-family;
                    margin-bottom: 8px; 
                    transition: $transition;
                    cursor: pointer;
        
                    &:hover {
                        color: $main-color;
                    }
                }
                span {
                    font-weight: 500;
                    color: $optional-color;
                    display: inline-block;
                    font-size: 15px;
                }
            }
            .accordion-content {
                display: none;

                p {
                    margin-top: 10px;
                    margin-bottom: 10px;
                    font-size: 15px;
                }
                .offer-btn {
                    font-size: 14px;
                    font-weight: 500;
                    color: $optional-color;
                    border-bottom: 1px solid $optional-color;
                    transition: $transition;
        
                    &:hover {
                        color: $main-color;
                        border-bottom: 1px solid $main-color;
                    }
                }
                &.show {
                    display: block;
                }
            }
        }
    }
    .all-offer-btn {
        margin-top: 30px;
    }
}

/*================================================
Reviews Area CSS
=================================================*/
.reviews-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.reviews-image {
    img {
        border-radius: 0 50px 0 0;
    }
}
.reviews-title {
    margin-bottom: 45px;
    margin-left: 30px;

    h3 {
        font-size: 45px;
        margin-bottom: 0;
    }
}
.reviews-slides {
    position: relative;
    margin-left: 30px;

    .reviews-feedback {
        position: relative;

        .single-feedback {
            position: relative;
            margin-top: 30px;
            margin-bottom: 30px;

            p {
                position: relative;
                line-height: 1.5;
                margin-left: auto;
                margin-right: auto;
                margin-bottom: 0;
                font-size: 20px;
                color: $black-color;
            }
            .icon {
                position: absolute;
                top: -50px;
                left: 0;
                opacity: 50%;

                i {
                    font-size: 120px;
                    color: $main-color;
                    margin-bottom: 20px;
                }
            }
        }
    }
    .reviews-thumbnails {
        position: relative;
        z-index: 1;

        .item {
            .img-fill {
                cursor: pointer;
                position: relative;
                text-align: center;

                img {
                    opacity: 0.7;
                    transition: $transition;
                    display: inline-block;
                    position: relative;
                    border-radius: 0 50px 0 0;
                    width: 130px;
                    transform: scale(.9);
                }
            }
            .title {
                opacity: 0;
                visibility: hidden;
                transition: $transition;
                margin: 0 -262px 30px;
                text-align: left;

                h3 {
                    margin-bottom: 0;
                    text-transform: capitalize;
                    font-size: 25px;
                    font-family: $main-font-family;
                }
                span {
                    display: block;
                    color: $main-color;
                    font-size: 15px;
                    margin-top: 8px;
                }
            }
            &.slick-center {
                .title {
                    opacity: 1;
                    visibility: visible;
                }
                .img-fill {
                    img {
                        opacity: 1;
                        transform: scale(1);
                    }
                }
            }
        }
    }
    .next-arrow, .prev-arrow {
        position: absolute;
        cursor: pointer;
        bottom: -175px;
        background: $main-color;
        border: none;
        color: $black-color;
        z-index: 9;
        border-radius: 10px 10px 0 10px;
        outline: 0 !important;
        transition: $transition;
        font-size: 25px;
        display: inline-block;
        height: 50px;
        width: 50px;
        line-height: 52px;
        opacity: 0;
        visibility: hidden;
    }
    .next-arrow {
        right: -20px;
    }
    .prev-arrow {
        left: -20px;
    }
    &:hover {
        .next-arrow, .prev-arrow {
            opacity: 1;
            visibility: visible;
        }
    }
    .slick-list {
        padding: {
            left: 0 !important;
            right: 0 !important;
        }
    }
}
.slick-slide {
    outline: 0;
}
.reviews-main-shape {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: -1;
    opacity: 50%;

    img {
        width: 100%;
    }
}
.clients-item {
    .item {
        margin-left: 30px;
    
        .title {
            margin-bottom: 45px;
        
            h3 {
                font-size: 45px;
                margin-bottom: 0;
            }
        }
        .single-feedback {
            position: relative;
            margin-top: 30px;
            margin-bottom: 30px;
    
            p {
                position: relative;
                line-height: 1.5;
                margin-left: auto;
                margin-right: auto;
                margin-bottom: 0;
                font-size: 20px;
                color: $black-color;
            }
            .icon {
                position: absolute;
                top: -50px;
                left: 0;
                opacity: 50%;
    
                i {
                    font-size: 120px;
                    color: $main-color;
                    margin-bottom: 20px;
                }
            }
        }
        .title-info {
            transition: $transition;

            h3 {
                margin-bottom: 0;
                text-transform: capitalize;
                font-size: 25px;
            }
            span {
                display: block;
                color: $paragraph-color;
                font-size: 14px;
                margin-top: 8px;
                font-weight: 500;
            }
        }
    }
}
.clients-slides {
    &.owl-theme {
        .owl-nav {
            margin-top: 0;

            [class*=owl-] {
                position: absolute;
                right: 60px;
                top: 80%;
                transform: translateY(-80%);
                margin: 0;
                outline: 0;
                width: 45px;
                height: 45px;
                line-height: 52px;
                transition: $transition;
                background: $main-color;
                color: $white-color;
                border-radius: 10px 10px 0 10px;

                &:hover, &:focus {
                    background-color: $black-color;
                }
                &.owl-next {
                    right: 0;

                    i {
                        position: relative;
                        top: 1px;
                        left: 2px;
                    }
                }
                i {
                    font-size: 25px;
                    font-weight: bold;
                    position: relative;
                    top: 1px;
                    right: 2px;
                }
            }
        }
    }
}

/*================================================
Philosophy Area CSS
=================================================*/
.philosophy-area {
    background-image: url(../../assets/images/philosophy/philosophy-bg.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    z-index: 1;
    overflow: hidden;
    background-attachment: fixed;

    &::before {
        position: absolute;
        content: "";
        left: 0;
        right: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background-color: $main-color;
        z-index: -1;
        opacity: .95;
    }
}
.philosophy-item {
    .philosophy-content {
        h3 {
            font-size: 38px;
            margin-bottom: 35px;
        }
        h4 {
            font-size: 22px;
            color: $black-color;
            font-style: italic;
            margin-bottom: 20px;
        }
        p {
            color: $black-color;
        }
        .philosophy-quote {
            background-color: $white-color;
            padding: 25px;
            position: relative;
            border-radius: 10px;

            i {
                font-size: 45px;
                color: $main-color;
                position: absolute;
                top: 10px;
                left: 25px;
                opacity: 50%;
            }
            p {
                position: relative;
                z-index: 1;
            }
        }
        .philosophy-btn {
            margin-top: 30px;
            text-align: right;

            .default-btn {
                background-color: $black-color;
                color: $white-color;

                i {
                    background-color: $main-color;
                }
                &:hover {
                    background-color: $white-color;
                    color: $black-color;
                }
            }
        }
    }
}
.philosophy-slides {
    &.owl-theme {
        .owl-dots {
            position: absolute;
            left: 0;
            right: 0;
            margin: auto;
            top: 90%;
            transform: translateY(-90%);
            text-align: left;

            .owl-dot {
                transition: $transition;

                span {
                    width: 5px;
                    height: 35px;
                    margin: 0 8px;
                    background: $white-color;
                    transition: $transition;
                    border-radius: 30px;
                }
                &:hover, &.active {
                    span {
                        background-color: $black-color;
                        height: 55px;

                        &::before {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }
        }
        .owl-nav.disabled+.owl-dots {
            margin-top: 0;
            max-width: 1370px;
            margin-left: auto;
        }
    }
}
.philosophy-main-shape {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    opacity: 15%;
    
    img {
        width: 100%;
    }
}

/*================================================
Products Area CSS
=================================================*/
.products-area {
    position: relative;
    z-index: 1;
    overflow: hidden;

    .container-fluid {
        padding-left: 0;
        padding-right: 0;
    }
}
.products-item {
    margin-bottom: 30px;
    transition: $transition;

    .products-image {
        border-radius: 25px;
        background-color: $white-color;
        box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
        margin-top: 10px;
        margin-bottom: 10px;
        position: relative;
        transition: $transition;

        img {
            border-radius: 25px;
        }
        .action-btn {
            position: absolute;
            left: 0;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            text-align: center;
            transition: $transition;
            opacity: 0;
            visibility: hidden;
            margin-top: 10px;

            .default-btn {
                background-color: $black-color;
                color: $white-color;

                i {
                    background-color: $main-color;
                    line-height: 40px;
                }
                &:hover {
                    background-color: $main-color;

                    i {
                        background-color: $white-color;
                    }
                }
            }
        }
    }
    .products-content {
        text-align: center;
        margin-top: 25px;

        h3 {
            font-size: 18px;
            margin-bottom: 12px;
            font-style: italic;

            a {
                color: $black-color;
            }
        }
        span {
            font-size: 14px;
            color: $paragraph-color;
            font-weight: 600;
        }
    }
    &:hover {
        .products-image {
            .action-btn {
                opacity: 1;
                visibility: visible;
                margin-top: 0;
            }
        }
    }
}
.book-store-btn {
    text-align: center;
    margin-top: 30px;

    .default-btn {
        background-color: $optional-color;
        color: $white-color;

        i {
            background-color: $main-color;
        }
        &:hover {
            background-color: $main-color;

            i {
                background-color: $optional-color;
                color: $white-color;
            }
        }
    }
}
.products-slides {
    &.owl-theme {
        .owl-nav {
            margin-top: 0;

            [class*=owl-] {
                position: absolute;
                left: -10px;
                top: 30%;
                transform: translateY(-30%);
                margin: 0;
                outline: 0;
                width: 45px;
                height: 45px;
                line-height: 52px;
                transition: $transition;
                background: $main-color;
                color: $white-color;
                border-radius: 50%;
                opacity: 0;
                visibility: hidden;

                &:hover, &:focus {
                    background-color: $black-color;
                }
                &.owl-next {
                    left: auto;
                    right: -10px;
                    i {
                        position: relative;
                        top: 1px;
                        left: 2px;
                    }
                }
                i {
                    font-size: 25px;
                    font-weight: bold;
                    position: relative;
                    top: 1px;
                    right: 2px;
                }
            }
        }
        &:hover {
            .owl-nav {
                [class*=owl-] {
                    opacity: 1;
                    visibility: visible;
                    left: 10px;
                    transition: $transition;
    
                    &.owl-next {
                        left: auto;
                        right: 10px;
                    }
                }
            }
        }
    }
    .owl-item {
        &.active {
            &.center {
                .products-item  {
                    transition: $transition;
                    transform: translateY(30px);
                }
            }
        }
    }
}
.products-main-shape {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: -1;
    opacity: 50%;

    img {
        width: 100%;
    }
}

/*================================================
Blog Area CSS
=================================================*/
.blog-area {
    .container-fluid {
        max-width: 2500px;
        margin: auto;
    }
}
.single-blog {
    margin-bottom: 30px;
    transition: $transition;
    position: relative;

    .blog-image {
        position: relative;
        transition: $transition;
        text-align: center;
        
        img {
            border-radius: 5px 5px 0 0;
        }
        .tag {
            display: inline-block;
            padding: 15px 22px;
            font-size: 15px;
            font-weight: 500;
            text-align: center;
            background-color: $optional-color;
            color: $main-color;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -30px;
            border-radius: 50%;
            transition: $transition;
            font-family: $heading-font-family;

            span {
                display: block;
            }
        }
    }
    .blog-content {
        background-color: $white-color;
        box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
        padding: 50px 30px 30px 30px;
        text-align: center;
        border-radius: 0 0 5px 5px;
        transition: $transition;
        max-width: 430px;
        margin: auto;

        h3 {
            font-size: 22px;
            margin-bottom: 18px;
            line-height: 1.8;

            a {
                color: $black-color;
            }
        }
        .blog-btn {
            .default-btn {
                background-color: $black-color;
                color: $white-color;

                i {
                    background-color: $main-color;
                    color: $white-color;
                }
                &:hover {
                    i {
                        background-color: $white-color;
                        color: $main-color;
                    }
                }
            }
        }
    }
    &:hover {
        transform: translateY(-10px);

        .blog-content {
            background-color: $main-color;
            max-width: 100%;
        }
    }
}
.single-blog-item {
    margin-bottom: 30px;
    transition: $transition;
    position: relative;

    .blog-image {
        position: relative;
        transition: $transition;
        text-align: center;
        
        img {
            border-radius: 5px 5px 0 0;
            display: inline-block;
        }
        .tag {
            display: inline-block;
            padding: 16px 25px;
            font-size: $font-size;
            font-weight: 500;
            text-align: center;
            background-color: $optional-color;
            color: $main-color;
            position: absolute;
            left: 30px;
            bottom: -45px;
            border-radius: 50%;
            transition: $transition;
            font-family: $heading-font-family;
            z-index: 1;

            span {
                display: block;
            }
        }
    }
    .blog-content {
        background-color: $white-color;
        box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
        padding: 65px 30px 30px 30px;
        border-radius: 0 0 5px 5px;
        transition: $transition;
        position: relative;

        .meta {
            position: absolute;
            top: 20px;
            right: 20px;

            p {
                margin-bottom: 0;
                color: $black-color;
                font-weight: 500;
                font-style: italic;

                a {
                    color: $black-color;
                }
            }
        }
        h3 {
            font-size: 25px;
            margin-bottom: 22px;
            line-height: 1.5;

            a {
                color: $black-color;
            }
        }
        .blog-btn {
            .default-btn {
                background-color: $black-color;
                color: $white-color;

                i {
                    background-color: $main-color;
                    color: $white-color;
                }
                &:hover {
                    i {
                        background-color: $white-color;
                        color: $main-color;
                    }
                }
            }
        }
    }
    &:hover {
        transform: translateY(-10px);

        .blog-content {
            background-color: $main-color;
        }
    }
}
.blog-slides {
    &.owl-theme {
        .owl-nav {
            margin-top: 0;

            [class*=owl-] {
                position: absolute;
                left: -10px;
                top: 30%;
                transform: translateY(-30%);
                margin: 0;
                outline: 0;
                width: 45px;
                height: 45px;
                line-height: 52px;
                transition: $transition;
                background: $main-color;
                color: $white-color;
                border-radius: 50%;
                opacity: 0;
                visibility: hidden;

                &:hover, &:focus {
                    background-color: $black-color;
                }
                &.owl-next {
                    left: auto;
                    right: -10px;
                    i {
                        position: relative;
                        top: 1px;
                        left: 2px;
                    }
                }
                i {
                    font-size: 25px;
                    font-weight: bold;
                    position: relative;
                    top: 1px;
                    right: 2px;
                }
            }
        }
        &:hover {
            .owl-nav {
                [class*=owl-] {
                    opacity: 1;
                    visibility: visible;
                    left: -20px;
                    transition: $transition;
    
                    &.owl-next {
                        left: auto;
                        right: -20px;
                    }
                }
            }
        }
    }
}

/*================================================
Services Area CSS
=================================================*/
.services-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
    background-color: #F9F9F9;
}
.tab {
    .tabs_item {
        display: none;
        
        &:first-child {
            display: block;
        }
    }
}
.services-list-tab {
    .tabs {
        padding-left: 0;
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        margin-right: -5px;
        margin-left: -5px;
        margin-bottom: 45px;
        
        li {
            flex: 24%;
            max-width: 25%;
            margin-left: 5px;
            margin-right: 5px;
            
            a {
                display: inline-block;
                color: $black-color;
                background-color: $white-color;
                box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.09);
                padding: 25px 15px 25px 75px;
                transition: $transition;
                width: 100%;
                border-radius: 15px;
                position: relative;
                text-align: center;

                i {
                    display: inline-block;
                    height: 50px;
                    width: 50px;
                    line-height: 50px;
                    background-color: $optional-color;
                    color: $main-color;
                    font-size: 25px;
                    position: absolute;
                    left: 15px;
                    top: 12px;
                    text-align: center;
                    border-radius: 15px;
                }
                span {
                    display: block;
                    font-size: $font-size;
                    font-weight: 600;
                }
            }
            &.current {
                a {
                    color: $white-color;
                    background-color: $optional-color;

                    i {
                        background-color: $main-color;
                        color: $white-color;
                    }
                }
            }
        }
    }
    .tab_content {
        .tabs_item {
            .services-tab-image {
                position: relative;

                img {
                    border-radius: 62% 38% 53% 47% / 48% 59% 41% 52%;
                }
                .services-tab-shape {
                    .shape-1 {
                        position: absolute;
                        left: 0;
                        top: 20px;
                        animation: moveleftbounce 5s linear infinite;
                    }
                    .shape-2 {
                        position: absolute;
                        left: -50px;
                        top: 30%;
                        transform: translateY(-30%);
                        max-width: 100px;
                        z-index: -1;
                        animation: movebounce 5s linear infinite;
                    }
                    .shape-3 {
                        position: absolute;
                        bottom: 70px;
                        left: 0;
                        animation: moveleftbounce 5s linear infinite;
                    }
                    .shape-4 {
                        position: absolute;
                        right: 60px;
                        bottom: 30px;
                        animation: movebounce 5s linear infinite;
                    }
                }
                .circle-shape {
                    position: absolute;
                    bottom: 50px;
                }
            }
            .services-tab-content {
                .services-content-image {
                    position: relative;
                    margin-bottom: 20px;
            
                    .sub-title {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: 35px;
                        font-size: 25px;
                        font-style: italic;
                    }
                }
                b {
                    font-weight: 400;
                    font-size: 15px;
                    color: $paragraph-color;
                }
                .services-quote {
                    background-color: $white-color;
                    padding: 25px;
                    position: relative;
                    border-radius: 10px;
                    margin-top: 20px;
        
                    i {
                        font-size: 45px;
                        color: $main-color;
                        position: absolute;
                        top: 10px;
                        left: 25px;
                        opacity: 50%;
                    }
                    p {
                        position: relative;
                        z-index: 1;
                    }
                }
                .services-btn {
                    margin-top: 25px;

                    .default-btn {
                        background-color: $optional-color;
                        color: $white-color;
                        padding: 15px 65px 14px 15px;

                        i {
                            background-color: $main-color;
                        }
                        &:hover {
                            background-color: $main-color;
                            color: $optional-color;

                            i {
                                background-color: $optional-color;
                                color: $white-color;
                            }
                        }
                    }
                }
            }
        }
    }
}
.services-main-shape {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    img {
        width: 100%;
    }
}

/*================================================
FAQS Area CSS
=================================================*/
.faqs-area {
    background-color: #F9F9F9;
    position: relative;
    z-index: 1;
    overflow: hidden;

    &.bg-color {
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background-color: $white-color;
            z-index: -1;
            height: 30px;
        }
        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: $white-color;
            z-index: -1;
            height: 170px;
        }
    }
    &.bg-ffffff {
        margin-top: 100px;
    }
}
.faq-item {
    padding-top: 100px;
    padding-bottom: 65px;

    .content {
        margin-bottom: 45px;
        
        h3 {
            font-size: 45px;
            margin-bottom: 0;

            span {
                font-weight: 500;
                font-style: italic;
            }
        }
    }
    .faq-btn {
        margin-top: 30px;
    }
}
.faq-accordion {
    .accordion {
        list-style-type: none;
        padding-left: 0;
        margin-bottom: 0;

        .accordion-item {
            display: block;
            background: $white-color;
            box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
            margin-bottom: 15px;
            border: none;

            &:last-child {
                margin-bottom: 0;
            }
        }
        .accordion-title {
            padding: 25px 40px 25px 20px;
            color: $black-color;
            position: relative;
            display: block;
            text-transform: capitalize;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: $transition;
            
            i {
                position: absolute;
                right: 20px;
                top: 50%;
                transform: translateY(-50%);
                color: $black-color;
                font-size: 15px;
                transition: $transition;
            }
            &.active {
                color: $main-color;

                i {
                    &::before {
                        content: "\ed91";
                        color: $main-color;
                    }
                }
            }
        }
        .accordion-content {
            display: none;
            position: relative;
            padding: 18px 20px;
            border-top: 1px solid $main-color;
            color: $paragraph-color;

            &.show {
                display: block;
            }
        }
    }
}
.faq-image {
    background-image: url(../../assets/images/faq.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%
}
.faqs-main-shape {
    position: absolute;
    bottom: 50px;
    z-index: -1;
    left: 0;
    opacity: 50%;
    width: 100%;

    img {
        width: 100%;
    }
}

/*================================================
Fun Facts Area CSS
=================================================*/
.fun-facts-area {
    background-image: url(../../assets/images/fun-facts-bg.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    margin-top: 100px;

    &.bg-top {
        margin-top: 0;
    }
}
.single-fun-fact {
    background-color: $white-color;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    padding: 30px 35px 30px 120px;
    border-radius: 30px 30px 0 30px;
    position: relative;
    margin-bottom: 30px;
    transition: $transition;

    .icon {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);

        i {
            display: inline-block;
            height: 85px;
            width: 85px;
            line-height: 85px;
            background-color: $main-color;
            color: $white-color;
            font-size: 45px;
            text-align: center;
            border-radius: 15px;
        }
    }
    h3 {
        font-size: 25px;
        margin-bottom: 5px;
        line-height: 1;

        .sign-icon {
            font-size: 25px;
            font-weight: 500;
        }
    }
    p {
        margin-bottom: 0;
    }
}

/*================================================
Pricing Area CSS
=================================================*/
.pricing-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.single-pricing-table {
    background-color: $main-color;
    padding: 30px 30px 15px;
    margin-bottom: 30px;
    border-radius: 30px 30px 0 30px;
    transition: $transition;

    .pricing-header {
        position: relative;
        background-color: $white-color;
        border-radius: 30px 30px 0 30px;
        padding: 30px 30px 30px 90px;

        i {
            font-size: 50px;
            color: $optional-color;
            position: absolute;
            left: 25px;
            top: 50%;
            transform: translateY(-50%);
        }
        h3 {
            font-size: 26px;
            margin-bottom: 15px;
        }
        span {
            font-size: $font-size;
            color: $paragraph-color;
            font-weight: 500;
        }
        .price {
            position: absolute;
            right: 30px;
            top: 30px;
            font-size: 20px;
            font-weight: bold;
        }
    }
    .pricing-features {
        padding-left: 0;
        margin-bottom: 0;
        padding-top: 30px;

        li {
            list-style-type: none;
            font-size: $font-size;
            font-weight: 500;
            margin-bottom: 18px;
            color: $black-color;
            position: relative;
            padding-left: 25px;
            transition: $transition;

            &:last-child {
                margin-bottom: 0;
            }
            i {
                position: absolute;
                left: 0;
                top: 1px;
                color: $white-color;
                transition: $transition;
            }
            &.color-gray {
                color: #eeeeee;
            }
        }
    }
    .pricing-btn {
        position: relative;
        top: 25px;
        text-align: center;

        .default-btn {
            background-color: $optional-color;
            color: $white-color;
            transition: $transition;

            i {
                background-color: $main-color;
            }
        }
    }
    &:hover {
        transform: translateY(-5px);
        background-color: $optional-color;

        .pricing-features {
            li {
                color: $white-color;

                i {
                    color: $main-color;
                }
            }
        }
        .pricing-btn {
            .default-btn {
                background-color: $main-color;
                color: $white-color;
    
                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}
.pricing-main-shape {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: -1;
    opacity: 50%;

    img {
        width: 100%;
    }
}

/*================================================
Download Area CSS
=================================================*/
.download-area {
    background-image: url(../../assets/images/download/download-bg.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    z-index: 1;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background-color: $white-color;
        z-index: -1;
        height: 50px;
    }
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: $white-color;
        z-index: -1;
        height: 50px;
    }
}
.download-main-content {
    h3 {
        font-size: 45px;
        margin-bottom: 30px;

        span {
            font-weight: 500;
        }
    }
    h4 {
        font-size: 25px;
        margin-bottom: 20px;
        font-style: italic;
        font-weight: 500;
    }
    p {
        margin-bottom: 0;
    }
    .download-btn {
        margin-top: 25px;
    }
}

/*================================================
Page Banner Area CSS
=================================================*/
.page-banner-area {
    position: relative;
    z-index: 1;
    overflow: hidden;

    .container-fluid {
        padding-left: 0;
    }
}
.page-banner-content {
    margin-left: 70px;

    h2 {
        font-size: 45px;
        margin-bottom: 20px;
    }
    ul {
        padding-left: 0;
        margin-bottom: 0;

        li {
            display: inline-block;
            list-style-type: none;
            margin-left: 35px;
            font-weight: 600;
            color: $optional-color;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -26px;
                top: 12px;
                height: 1px;
                width: 15px;
                background: $optional-color;
            }
            &:first-child {
                margin-left: 0;

                &::before {
                    display: none;
                }
            }
            a {
                display: block;
                color: $optional-color;
            }
        }
    }
}
.page-banner-image {
    position: relative;

    .image-shape {
        position: absolute;
        top: -15px;
        right: -50px;
        animation: movebounce 5s linear infinite;
    }
}
.page-banner-shape {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 30%;
    z-index: -1;

    img {
        width: 100%;
    }
}
.page-banner-with-full-image {
    position: relative;
    z-index: 1;
    background-image: url(../../assets/images/page-banner/page-banner-bg-1.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 100%;
    padding-top: 140px;
    padding-bottom: 150px;
    background-attachment: fixed;

    &::before {
        position: absolute;
        content: "";
        height: 100%;
        width: 100%;
        background-color: #000000;
        left: 0;
        right: 0;
        top: 0;
        z-index: -1;
        opacity: .60;
    }
    &.item-bg1 {
        background-image: url(../../assets/images/page-banner/page-banner-bg-2.jpg);
    }
    &.item-bg2 {
        background-image: url(../../assets/images/page-banner/page-banner-bg-3.jpg);
    }
    &.item-bg3 {
        background-image: url(../../assets/images/page-banner/page-banner-bg-4.jpg);
    }
    &.item-bg4 {
        background-image: url(../../assets/images/page-banner/page-banner-bg-5.jpg);
    }
}
.page-banner-content-two {
    text-align: center;

    h2 {
        font-size: 45px;
        margin-bottom: 20px;
        color: $white-color;
    }
    ul {
        padding-left: 0;
        margin-bottom: 0;

        li {
            display: inline-block;
            list-style-type: none;
            margin-left: 35px;
            font-weight: 600;
            color: $white-color;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -26px;
                top: 12px;
                height: 1px;
                width: 15px;
                background: $white-color;
            }
            &:first-child {
                margin-left: 0;

                &::before {
                    display: none;
                }
            }
            a {
                display: block;
                color: $main-color;
            }
        }
    }
}

/*================================================
Coaches Area CSS
=================================================*/
.single-coaches {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    
    .image {
        position: relative;

        .social-link {
            position: absolute;
            right: 20px;
            bottom: 35px;
            padding-left: 0;
            margin-bottom: 0;
            list-style-type: none;
    
            li {
                display: block;
                margin-bottom: 8px;
    
                &:last-child {
                    margin-bottom: 0;
                }
                a {
                    width: 45px;
                    height: 45px;
                    text-align: center;
                    color: $optional-color;
                    background-color: $white-color;
                    font-size: 20px;
                    position: relative;
                    border-radius: 50px;
                    transform: scaleY(0);
                    transition: $transition;
    
                    i {
                        position: absolute;
                        left: 0;
                        top: 50%;
                        right: 0;
                        transform: translateY(-48%);
                    }
                    &:hover {
                        background-color: $main-color;
                        color: $white-color;
                    }
                }
                &:nth-child(2), &:nth-child(4), &:nth-child(6), &:nth-child(8) {
                    a {
                        transform: scaleX(0);
                    }
                }
            }
        }
    }
    .content {
        background-color: #f9f9f9;
        text-align: left;
        padding: 30px;
        position: relative;

        h3 {
            font-size: 28px;
            margin-bottom: 0;
        }
        span {
            margin-top: 15px;
            display: block;
            font-weight: 600;
            color: $paragraph-color;
        }
        i {
            display: inline-block;
            width: 45px;
            height: 45px;
            line-height: 45px;
            text-align: center;
            background-color: $main-color;
            transition: $transition;
            color: $white-color;
            border-radius: 50px;
            position: absolute;
            right: 20px;
            top: -25px;
            font-size: 25px;
        }
    }
    &:hover {
        .content {
            i {
                background-color: $optional-color;
                color: $white-color;
            }
        }
        .image {
            .social-link {
                li {
                    a {
                        transform: scale(1) !important;
                    }
                }
            }
        }
    }
}

/*================================================
Coaches Details Area CSS
=================================================*/
.coaches-details-image {
    img {
        border-radius: 15px;
    }
}
.coaches-details-content {
    h3 {
        font-size: 35px;
        margin-bottom: 18px;
    }
    b {
        font-weight: 600;
        color: $main-color;
        display: inline-block;
        margin-bottom: 16px;
    }
    p {
        margin-bottom: 20px;
    }
    .information {
        padding-left: 0;
        margin-bottom: 0;

        li {
            list-style-type: none;
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 0;
            }
            span {
                color: $paragraph-color;
                font-weight: 600;
            }
            a {
                color: $optional-color;
                font-weight: 500;
                
                &:hover {
                    color: $main-color;
                    letter-spacing: 1px;
                }
            }
        }
    }
    .social-link {
        padding-left: 0;
        margin-bottom: 0;
        margin-top: 25px;

        li {
            list-style-type: none;
            display: inline-block;
            margin-right: 8px;

            &:last-child {
                margin-right: 0;
            }
            a {
                i {
                    display: inline-block;
                    width: 38px;
                    height: 38px;
                    line-height: 38px;
                    text-align: center;
                    background-color: #e1e1e1;
                    transition: $transition;
                    color: $optional-color;
                    border-radius: 50px;
                    transition: $transition;
                    font-size: 18px;

                    &:hover {
                        background-color: $main-color;
                        color: $white-color;
                        transform: translateY(-5px);
                    }
                }
            }
        }
    }
}

/*================================================
Events Area CSS
=================================================*/
.psylo-grid-sorting {
    margin-bottom: 30px;

    .result-count {
        p {
            .count {
                font-weight: 700;
                color: $black-color;
            }
        }
    }
    .ordering {
        text-align: right;
        .select-box {
            display: flex;
            align-items: center;
            justify-content: end;
        }
        label {
            display: inline-block;
            margin-bottom: 0;
            color: $main-color;
            margin-right: 5px;
            font-weight: 600;
        }
        .nice-select {
            display: inline-block;
            width: 215px;
            background: #f8f8f8;
            border-color: #eeeeee;
            color: $black-color;
            transition: $transition;
            padding: 0 0 0 12px;
            height: 55px;
            line-height: 55px;
            font-weight: bold;
            border-radius: 0;

            .list {
                background-color: $white-color;
                border-radius: 0;
                box-shadow: 0 10px 30px rgba(0,0,0,.2);
                list-style-type: none;
                border: none;
                width: 100%;
                margin-top: 0;
                margin-bottom: 0;
                padding-left: 0;
                padding-top: 10px;
                padding-bottom: 10px;

                .option {
                    line-height: 38px;
                    min-height: 38px;
                    color: $black-color;
                    position: relative;
                    transition: $transition;
                    padding-left: 15px;
                    padding-right: 25px;
                    font-weight: 600;

                    &.selected {
                        &.focus {
                            color: $main-color;
                            background-color: $white-color;
                        }
                    }
                    &:hover {
                        background-color: $main-color;
                        color: $white-color;
                    }
                }
            }
            &:after {
                border-color: $black-color;
                height: 8px;
                width: 8px;
                margin-top: -5px;
            }
            &:hover {
                border-color: $optional-color;
                background-color: transparent;
            }
        }
    }
    .search-form {
        position: relative;
        
        .search-field {
            background-color: #f9f9f9;
            height: 60px;
            padding: 8px 15px;
            border: 1px solid #f9f9f9;
            width: 100%;
            display: block;
            outline: 0;
            transition: $transition;
            border-radius: 5px;
            color: $optional-color;

            &::placeholder {
                color: $paragraph-color;
            }
            &:focus {
                border-color: $main-color;
            }
        }
        button {
            border: none;
            background-color: $main-color;
            color: $white-color;
            height: 40px;
            width: 40px;
            position: absolute;
            right: 10px;
            padding: 0;
            transition: $transition;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            border-radius: 5px;
            cursor: pointer;

            i {
                position: absolute;
                left: 0;
                right: 0;
                top: 52%;
                transform: translateY(-52%);
            }
            &:hover, &:focus {
                background-color: $optional-color;
                color: $white-color;
            }
        }
    }
}
.single-events-box {
    transition: $transition;
    background-color: $white-color;
    margin-bottom: 30px;

    .image {
        position: relative;

        .date {
            position: absolute;
            right: 10px;
            bottom: 10px;
            display: inline-block;
            background-color: $white-color;
            color: $paragraph-color;
            padding: 8px 20px;
            border-radius: 5px;
            font-size: $font-size;
            font-weight: bold;
        }
        img {
            border-radius: 5px 5px 0 0;
        }
    }
    .content {
        padding: 20px;
        position: relative;
        background-color: #f3f3f3;
        border-radius: 0 0 5px 5px;

        h3 {
            margin-bottom: 0;
            line-height: 1.3;
            font-size: 25px;

            a {
                display: inline-block;
            }
        }
        .location {
            display: block;
            color: $optional-color;
            margin-top: 20px;
            position: relative;
            padding-left: 22px;
            font-size: $font-size;
            font-weight: bold;
            
            i {
                color: $main-color;
                position: absolute;
                left: 0;
                top: 2px;
                font-size: 18px;
            }
        }
    }
    &:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        transform: translateY(-10px);
    }
}

/*================================================
Events Details Area CSS
=================================================*/
.events-details-image {
    position: relative;
    margin-bottom: 50px;

    #timer {
        position: absolute;
        z-index: 2;
        right: 0;
        left: 0;
        bottom: 50px;

        div {
            background-color: $main-color;
            color: $white-color;
            width: 150px;
            height: 150px;
            z-index: 1;
            border-radius: 10px;
            font-size: 60px;
            font-weight: 800;
            margin-left: 8px;
            margin-right: 8px;

            span {
                display: block;
                margin-top: -6px;
                font-size: $font-size;
                font-weight: 600;
            }
        }
    }
}
.events-details-header {
    margin-bottom: 30px;

    ul {
        margin-bottom: 0;
        list-style-type: none;
        background-color: #f8f9f8;
        padding: 30px;
        
        li {
            display: inline-block;
            margin-right: 20px;
            color: $optional-color;
            position: relative;
            padding-left: 25px;
            font-size: $font-size;
            font-weight: 600;
            
            i {
                color: $main-color;
                position: absolute;
                left: 0;
                top: 2px;
                font-size: 18px;
            }
            &:last-child {
                margin-right: 0;
            }
        }
    }
}
.events-details-location {
    margin-bottom: 30px;

    iframe {
        height: 450px;
        width: 100%;
        border: none;
    }
}
.events-details-desc {
    h3 {
        margin-bottom: 20px;
        font-size: 25px;

        &:not(:first-child) {
            margin-top: 20px;
        }
    }
}
.events-details-info {
    background-color: #f8f9f8;
    border-radius: 5px;
    padding: 35px;

    .info {
        margin-bottom: 0;
        list-style-type: none;
        padding-left: 0;

        li {
            border-bottom: 1px solid #e5e5e7;
            color: $optional-color;
            font-size: $font-size;
            font-weight: 600;
            padding-top: 15px;
            padding-bottom: 15px;
            
            span {
                font-weight: bold;
                color: $black-color;
                position: relative;
            }
            &.price {
                padding-bottom: 10px;
                color: $main-color;
                font-size: 28px;
                font-weight: bold;

                span {
                    color: $black-color;
                    font-size: $font-size;
                    font-weight: bold;
                }
            }
            &:first-child {
                padding-top: 0;
            }
        }
    }
    .events-btn-box {
        text-align: center;
        margin-top: 25px;
        
        p {
            margin-top: 20px;
            font-weight: 600;

            a {
                color: $main-color;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    .events-share {
        text-align: center;
        margin-top: 20px;
        
        .share-info {
            display: inline-block;
            position: relative;
            cursor: pointer;

            span {
                display: inline-block;
                color: $main-color;
                line-height: 1;
                font-size: $font-size;
                font-weight: bold;
            }
            .social-link {
                padding-left: 0;
                list-style-type: none;
                margin-bottom: 0;
                margin-top: 12px;
                
                li {
                    display: inline-block;
                    margin-right: 5px;
        
                    &:last-child {
                        margin-right: 0;
                    }
                    a {
                        i {
                            display: inline-block;
                            width: 45px;
                            height: 45px;
                            line-height: 45px;
                            text-align: center;
                            background-color: #e1e1e1;
                            font-size: 20px;
                            color: $black-color;
                            position: relative;
                            border-radius: 50px;
                            transition: $transition;

                            &:hover {
                                background-color: $main-color;
                                color: $white-color;
                                transform: translateY(-5px);
                            }
                        }
                    }
                }
            }
        }
    }
}

/*================================================
Success Story Area CSS
=================================================*/
.success-story-image {
    position: relative;
    overflow: hidden;
    
    img {
        transition: $transition;
    }
    .video-btn {
        display: inline-block;
        width: 80px;
        height: 80px;
        line-height: 90px;
        background-color: $main-color;
        border-radius: 50px;
        color: $white-color;
        position: absolute;
        z-index: 1;
        left: 0;
        right: 0;
        margin: auto;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;

        i {
            font-size: 30px;
            position: relative;
            top: 0;
            left: 4px;
        }
        &::after {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            z-index: -1;
            bottom: 0;
            left: 0;
            border-radius: 50px;
            border: 1px solid $main-color;
            animation: ripple 2s linear 1s infinite;
        }
        &::before {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            z-index: -1;
            bottom: 0;
            left: 0;
            border-radius: 50px;
            border: 1px solid $main-color;
            animation: ripple 2s linear infinite;
        }
        &:hover {
            background-color: $white-color;
            color: $main-color;
        }
    }
    &:hover {
        img {
            transform: scale(1.1);
        }
    }
}
.success-story-content {
    h3 {
        font-size: 25px;
        margin-top: 30px;
        margin-bottom: 20px;
    }
    p {
        margin-bottom: 0;
    }
}

/*================================================
Courses Area CSS
=================================================*/
.courses-area {
    .container-fluid {
        max-width: 2500px;
        margin: auto;
    }
}
.single-courses-box {
    margin-bottom: 30px;
    position: relative;
    border-radius: 5px;
    background-color: $white-color;
    box-shadow: 0px 8px 16px 0px rgba(146, 184, 255, 0.2);
    transition: $transition;

    .courses-image {
        border-radius: 5px 5px 0 0;
        position: relative;
        z-index: 1;

        .image {
            border-radius: 5px 5px 0 0;

            img {
                border-radius: 5px 5px 0 0;
            }
        }
        .price {
            display: inline-block;
            background-color: $main-color;
            color: $white-color;
            width: 65px;
            height: 65px;
            border-radius: 50%;
            position: absolute;
            right: 20px;
            bottom: -32.5px;
            text-align: center;
            line-height: 65px;
            font-size: 25px;
            font-weight: 600;
        }
    }
    .courses-content {
        border-radius: 0 0 5px 5px;
        padding: 30px;

        .course-author {
            margin-bottom: 20px;

            img {
                width: 45px;
                height: 45px;
                margin-right: 10px;
            }
            span {
                color: $main-color;
                font-size: $font-size;
                font-weight: 600;
            }
        }
        h3 {
            margin-bottom: 15px;
            line-height: 1.5;
            font-size: 22px;
            
            a {
                display: inline-block;
            }
        }
        .courses-box-footer {
            list-style-type: none;
            padding-left: 0;
            margin-bottom: 0;
            margin-left: -7px;
            margin-right: -7px;
            margin-top: 20px;
            
            li {
                color: $optional-color;
                font-size: 15px;
                font-weight: 500;
                position: relative;
                padding-left: 32px;
                padding-right: 8px;

                i {
                    color: $main-color;
                    position: absolute;
                    left: 6px;
                    top: 0;
                    font-size: 20px;
                }
            }
        }
    }
    &:hover {
        transform: translateY(-10px);
    }
}

/*================================================
Membership Levels Area CSS
=================================================*/
.membership-levels-table {
    .table {
        --bs-table-bg: transparent;
    }
    .table {
        margin-bottom: 0;

        thead {
            th {
                vertical-align: middle;
                background-color: $main-color;
                white-space: nowrap;
                padding: 20px;
                text-align: center;
                color: rgba(255, 255, 255, .9);
                border-left: 1px solid #e98f8f;
                border-right: 1px solid #e98f8f;
                border-bottom: none;
                .desc-1 {
                    color: $white-color;
                    margin-bottom: 0;
                    font-size: 35px;
                    font-weight: bold;
                    font-family: $main-font-family;
                    display: block;
                }
                .desc-2 {
                    color: $white-color;
                    margin-top: 10px;
                    margin-bottom: 10px;
                    font-size: $font-size;
                    font-weight: bold;
                    font-family: $main-font-family;
                    display: block;
                }
                &:first-child {
                    border-left: none;
                }
                &:last-child {
                    border-right: none;
                }
            }
        }
        tbody {
            tr {
                td {
                    padding: 20px 25px;
                    white-space: nowrap;
                    vertical-align: middle;
                    border: none;
                    text-align: center;
                    font-size: $font-size;
                    font-weight: 600;
                    
                    &:first-child {
                        text-align: left;
                    }
                    &.item-check {
                        i {
                            width: 20px;
                            height: 20px;
                            line-height: 20px;
                            border-radius: 100%;
                            background-color: #0eb582;
                            color: $white-color;
                            display: block;
                            margin: 0 auto;
                        }
                    }
                    &.item-none {
                        i {
                            width: 20px;
                            height: 20px;
                            line-height: 20px;
                            border-radius: 100%;
                            background-color: #d85554;
                            color: $white-color;
                            display: block;
                            margin: 0 auto;
                        }
                    }
                    .select-btn {
                        display: inline-block;
                        color: $black-color;
                        background-color: #efefef;
                        padding: 15px 35px;
                        border-radius: 5px;
                        margin-top: 10px;
                        font-size: $font-size;
                        font-weight: bold;

                        &:hover {
                            color: $white-color;
                            background-color: $main-color
                        }
                    }
                }
            }
        }
    }
}

/*================================================
Become Coaches Area CSS
=================================================*/
.become-coaches-image {
    img {
        border-radius: 10px;
    }
}
.become-coaches-form {
    padding: 35px;
    border-radius: 10px;
    background-color: $white-color;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    
    .become-coaches-title {
        margin-bottom: 30px;

        h3 {
            font-size: 30px;
            margin-bottom: 15px;
        }
        span {
            color: $paragraph-color;
        }
    }
    form {
        .form-group {
            margin-bottom: 25px;
            
            .form-control {
                height: 60px;
                padding: 20px;
                line-height: initial;
                color: $black-color;
                background-color: transparent;
                border: 1px solid #eeeeee;
                border-radius: 15px;
                box-shadow: unset;
                transition: $transition;
                font-size: $font-size;
                font-weight: 500;

                &:focus {
                    border: 1px solid $main-color;
                    background-color: transparent;

                    &::placeholder {
                        color: transparent;
                        transition: $transition;
                    }
                }
            }
        }
        textarea {
            &.form-control {
                height: 120px !important;
            }
        }
        .default-btn {
            border: none;
            width: 100%;
            padding: 20px 30px;
        }
    }
}

/*================================================
Courses Details Area CSS
=================================================*/
.courses-details-image {
    position: relative;
    overflow: hidden;
    
    img {
        transition: $transition;
    }
    .video-btn {
        display: inline-block;
        width: 80px;
        height: 80px;
        line-height: 90px;
        background-color: $main-color;
        border-radius: 50px;
        color: $white-color;
        position: absolute;
        z-index: 1;
        left: 0;
        right: 0;
        margin: auto;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;

        i {
            font-size: 30px;
            position: relative;
            top: 0;
            left: 4px;
        }
        &::after {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            z-index: -1;
            bottom: 0;
            left: 0;
            border-radius: 50px;
            border: 1px solid $main-color;
            animation: ripple 2s linear 1s infinite;
        }
        &::before {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            z-index: -1;
            bottom: 0;
            left: 0;
            border-radius: 50px;
            border: 1px solid $main-color;
            animation: ripple 2s linear infinite;
        }
        &:hover {
            background-color: $white-color;
            color: $main-color;
        }
    }
    &:hover {
        img {
            transform: scale(1.1);
        }
    }
}
.courses-details-desc {
    margin-top: 50px;

    .nav {
        margin-bottom: 0;
        list-style-type: none;
        background-color: #f8f9f8;
        border: none;
        border-radius: 5px;
        
        .nav-item {
            margin-bottom: 0;
            margin-right: 40px;
            
            .nav-link {
                border-radius: 0;
                border: none;
                padding: 0;
                transition: $transition;
                padding: 30px;
                background-color: #f8f9f8;
                position: relative;
                border-radius: 5px;
                font-size: $font-size;
                font-weight: 600;
                
                &::before {
                    content: '';
                    bottom: 0;
                    height: 2px;
                    background-color: $main-color;
                    position: absolute;
                    transition: $transition;
                    left: 0;
                    width: 100%;
                    transform: scaleX(0);
                }
                &:hover, &.active {
                    color: $black-color;

                    &::before {
                        transform: scaleX(1);
                    }
                }
            }
            &:last-child {
                margin-right: 0;
            }
        }
    }
    .tab-content {
        margin-top: 40px;

        .courses-overview {
            h3 {
                margin-bottom: 20px;
                font-size: 25px;
                
                &:not(:first-child) {
                    margin-top: 20px;
                }
            }
        }
        .courses-curriculum {
            border: 1px solid #eeeeee;
            border-radius: 5px;
            padding: 30px;

            h3 {
                margin-bottom: 18px;
                font-size: 20px;
                
                &:not(:first-child) {
                    margin-top: 20px;
                }
            }
            ul {
                padding-left: 0;
                margin-bottom: 0;
                list-style-type: none;

                li {
                    display: block;

                    a {
                        background-color: #f8f9f8;
                        color: $black-color;
                        padding-left: 30px;
                        padding-right: 30px;
                        padding-top: 15px;
                        padding-bottom: 15px;
                        margin-left: -30px;
                        margin-right: -30px;
                        
                        .courses-name {
                            font-size: 15px;
                            font-weight: 500;
                        }
                        .courses-meta {
                            text-align: right;

                            .questions {
                                display: inline-block;
                                background: #e3f1f2;
                                color: #2dbbc4;
                                text-transform: lowercase;
                                border-radius: 3px;
                                margin-right: 6px;
                                padding: 2px 10px 1.5px;
                                font-size: 15px;
                                font-weight: 600;
                            }
                            .duration {
                                display: inline-block;
                                background: #f7e7e8;
                                color: $main-color;
                                text-transform: lowercase;
                                border-radius: 3px;
                                padding: 2px 10px 1.5px;
                                font-size: 15px;
                                font-weight: 600;
                            }
                            .status {
                                display: inline-block;
                                background: $black-color;
                                color: $white-color;
                                border-radius: 3px;
                                margin-left: 6px;
                                padding: 2px 10px 1.5px;
                                font-size: 15px;
                                font-weight: 600;
                                &.locked {
                                    color: $black-color;
                                    background-color: transparent;
                                    padding: 0;
                                    margin-left: 8px;
                                    border-radius: 0;
                                    position: relative;
                                    top: 1px;
                                    font-size: 18px;
                                    font-weight: normal;
                                }
                            }
                        }
                        &:hover {
                            color: $main-color;
                        }
                    }
                    &:nth-child(2), &:nth-child(4), &:nth-child(6), &:nth-child(8), &:nth-child(10), &:nth-child(12), &:nth-child(14), &:nth-child(16), &:nth-child(18), &:nth-child(20), &:nth-child(22), &:nth-child(24), &:nth-child(26), &:nth-child(28), &:nth-child(30), &:nth-child(32), &:nth-child(34), &:nth-child(36), &:nth-child(38), &:nth-child(40), &:nth-child(42), &:nth-child(44), &:nth-child(46), &:nth-child(48), &:nth-child(50), &:nth-child(52), &:nth-child(54), &:nth-child(56), &:nth-child(58), &:nth-child(60), &:nth-child(62), &:nth-child(64), &:nth-child(66), &:nth-child(68), &:nth-child(70), &:nth-child(72), &:nth-child(74), &:nth-child(76), &:nth-child(78), &:nth-child(80), &:nth-child(82), &:nth-child(84), &:nth-child(86), &:nth-child(88), &:nth-child(90), &:nth-child(92), &:nth-child(94), &:nth-child(96), &:nth-child(98), &:nth-child(100) {
                        a {
                            background-color: $white-color;
                        }
                    }
                }
            }
        }
        .courses-instructor {
            .instructor-image {
                img {
                    border-radius: 15px;
                }
            }
            .instructor-content {
                h3 {
                    font-size: 25px;
                    margin-bottom: 18px;
                }
                b {
                    font-weight: 600;
                    color: $main-color;
                    display: inline-block;
                    margin-bottom: 16px;
                }
                p {
                    margin-bottom: 20px;
                }
                .social-link {
                    padding-left: 0;
                    margin-bottom: 0;
                    margin-top: 25px;
            
                    li {
                        list-style-type: none;
                        display: inline-block;
                        margin-right: 8px;
            
                        &:last-child {
                            margin-right: 0;
                        }
                        a {
                            i {
                                display: inline-block;
                                width: 38px;
                                height: 38px;
                                line-height: 38px;
                                text-align: center;
                                background-color: #e1e1e1;
                                transition: $transition;
                                color: $optional-color;
                                border-radius: 50px;
                                transition: $transition;
                                font-size: 18px;
            
                                &:hover {
                                    background-color: $main-color;
                                    color: $white-color;
                                    transform: translateY(-5px);
                                }
                            }
                        }
                    }
                }
            }
        }
        .courses-reviews {
            h3 {
                margin-bottom: 0;
                display: inline-block;
                margin-right: 15px;
                font-size: 22px;
                font-weight: bold;
                font-family: $main-font-family;
            }
            .rating {
                display: inline-block;
                position: relative;
                top: 1px;
                
                span {
                    font-size: 19px;
                    color: #cecfd2;
                    margin-right: -2px;
    
                    &.checked {
                        color: orange;
                    }
                }
            }
            .rating-count {
                margin-top: 10px;
                margin-bottom: 20px;
                
                span {
                    display: block;
                    font-size: $font-size;
                    color: $optional-color;
                    font-weight: 500;
                }
            }
            .row {
                overflow: hidden;
                margin-left: 0;
                margin-right: 0;
            }
            .side {
                float: left;
                width: 9%;
                margin-top: 10px;

                div {
                    font-size: $font-size;
                    font-weight: bold;
                }
            }
            .middle {
                margin-top: 14px;
                float: left;
                width: 82%;
            }
            .right {
                text-align: right;
            }
            .bar-container {
                width: 100%;
                background-color: #f1f1f1;
                text-align: center;
                color: $white-color;
                border-radius: 5px;
            }
            .bar-5 {
                width: 100%;
                height: 18px;
                background-color: #4CAF50;
                border-radius: 5px;
            }
            .bar-4 {
                width: 75%;
                height: 18px;
                background-color: #2196F3;
                border-radius: 5px;
                border-radius: 5px;
            }
            .bar-3 {
                width: 50%;
                height: 18px;
                background-color: #00bcd4;
                border-radius: 5px;
            }
            .bar-2 {
                width: 25%;
                height: 18px;
                background-color: #ff9800;
                border-radius: 5px;
            }
            .bar-1 {
                width: 0;
                height: 18px;
                background-color: #f44336;
                border-radius: 5px;
            }
        }
        .courses-review-comments {
            margin-top: 40px;
        
            h3 {
                border-bottom: 1px solid #f3f3f3;
                padding-bottom: 12px;
                margin-bottom: 10px;
                font-size: 22px;
                font-weight: bold;
                font-family: $main-font-family;
            }
            .user-review {
                border-bottom: 1px solid #f3f3f3;
                padding: 20px 0 20px 110px;
                position: relative;
        
                img {
                    position: absolute;
                    left: 0;
                    top: 20px;
                    width: 90px;
                    height: 90px;
                    border-radius: 5px;
                }
                .sub-comment {
                    margin-bottom: 8px;
                    font-weight: bold;
                }
                p {
                    font-size: 14px;
                }
                .review-rating {
                    display: block;
                    margin-bottom: 8px;
        
                    .review-stars {
                        display: inline-block;
        
                        i {
                            color: #cecfd2;
                            font-size: 18px;
                            display: inline-block;
                            margin-right: -3px;
            
                            &.checked {
                                color: orange;
                            }
                        }
                    }
                    span {
                        color: $black-color;
                        position: relative;
                        top: -2px;
                        font-weight: 800;
                        margin-left: 5px;
                    }
                }
            }
        }
    }
}
.courses-details-info {
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 12px 12px 50px 12px;
    margin-top: 50px;

    .image {
        text-align: center;
        position: relative;
        
        .video-btn {
            display: inline-block;
            width: 70px;
            height: 70px;
            line-height: 70px;
            background-color: $white-color;
            border-radius: 50%;
            color: $black-color;
            position: absolute;
            z-index: 1;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            left: 0;
            margin: auto;
            text-align: center;
            transition: $transition;

            i {
                font-size: 35px;
                position: relative;
                top: 8px;
                left: 5px;
                color: $main-color;
                transition: $transition;
            }
            &::before {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                z-index: -1;
                bottom: 0;
                left: 0;
                border-radius: 50%;
                border: 2px solid $white-color;
                animation: ripple 1s linear infinite;
            }
            &:hover {
                background-color: $main-color;
                
                i {
                    color: $white-color;
                }
            }
        }
    }
    .info {
        margin-bottom: 0;
        list-style-type: none;
        padding: 30px 15px;
        
        li {
            border-bottom: 1px solid #e5e5e7;
            color: $optional-color;
            font-size: $font-size;
            font-weight: 500;
            padding-top: 15px;
            padding-bottom: 15px;
            
            span {
                font-weight: 600;
                color: $black-color;
            }
            &.price {
                padding-bottom: 10px;
                color: $main-color;
                font-size: 28px;
                font-weight: 800;
                
                span {
                    color: $black-color;
                    font-size: $font-size;
                    font-weight: bold;
                }
            }
            &:first-child {
                padding-top: 0;
            }
        }
    }
    .courses-btn-box {
        text-align: center;
    }
    .courses-share {
        text-align: center;
        margin-top: 25px;
        
        .share-info {
            display: inline-block;
            position: relative;
            cursor: pointer;

            span {
                display: inline-block;
                color: $main-color;
                line-height: 1;
                font-size: $font-size;
                font-weight: bold;
            }
            .social-link {
                padding-left: 0;
                list-style-type: none;
                margin-bottom: 0;
                margin-top: 12px;
                
                li {
                    display: inline-block;
                    margin-right: 5px;
        
                    &:last-child {
                        margin-right: 0;
                    }
                    a {
                        i {
                            display: inline-block;
                            width: 45px;
                            height: 45px;
                            line-height: 45px;
                            text-align: center;
                            background-color: #e1e1e1;
                            font-size: 20px;
                            color: $black-color;
                            position: relative;
                            border-radius: 50px;
                            transition: $transition;

                            &:hover {
                                background-color: $main-color;
                                color: $white-color;
                                transform: translateY(-5px);
                            }
                        }
                    }
                }
            }
        }
    }
}

/*================================================
Services Details Area CSS
=================================================*/
.services-details-desc {
    .content-image {
        position: relative;
        margin-bottom: 25px;

        .sub-title {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 35px;
            font-size: 30px;
            font-style: italic;
        }
    }
    .overview-image {
        margin-top: 25px;
        margin-bottom: 25px;
    }
    .overview-content {
        margin-top: 20px;
        margin-bottom: 25px;
        h3 {
            font-size: 25px;
            margin-bottom: 20px;
        }
        .list {
            padding-left: 0;
            margin-bottom: 0;

            li {
                font-size: 15px;
                color: $paragraph-color;
                font-weight: 500;
                position: relative;
                margin-bottom: 15px;
                list-style-type: none;
                padding-left: 25px;

                &:last-child {
                    margin-bottom: 0;
                }
                i {
                    position: absolute;
                    left: 0;
                    top: 1px;
                    color: $main-color;
                    font-size: $font-size;
                }
            }
        }
    }
    .color-text {
        color: $black-color;
    }
    .overview-quote {
        background-color: $main-color;
        padding: 25px;
        position: relative;
        border-radius: 10px;
        margin-top: 20px;
        margin-bottom: 20px;

        i {
            font-size: 45px;
            color: $white-color;
            position: absolute;
            top: 10px;
            left: 25px;
            opacity: 50%;
        }
        p {
            position: relative;
            z-index: 1;
            color: $white-color;
        }
    }
}
.services-details-info {
    padding-left: 15px;

    .services-list {
        padding-left: 0;
        list-style-type: none;
        margin-bottom: 30px;

        li {
            margin-bottom: 15px;
            
            &:last-child {
                margin-bottom: 0;
            }
            a {
                display: block;
                background-color: $white-color;
                box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
                padding: 20px 30px;
                position: relative;
                font-size: $font-size;
                font-weight: bold;
                border-radius: 10px 10px 0 10px;
                
                i {
                    position: absolute;
                    right: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                    display: inline-block;
                    height: 50px;
                    width: 50px;
                    line-height: 50px;
                    font-size: 25px;
                    border-radius: 10px;
                    text-align: center;
                    background-color: $main-color;
                    color: $white-color;
                }
                &:hover, &.active {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
    .choose-us-services {
        h2 {
            font-size: 25px;
            margin-bottom: 0;
        }
        .choose-us-content {
            margin-top: 30px;
            background-color: $main-color;
            padding: 30px;
            border-radius: 20px 20px 0 0;
        
            .icon {
                position: relative;
                padding-left: 85px;
                padding-top: 30px;
                padding-bottom: 30px;
                padding-right: 30px;
                background-color: $white-color;
                border-radius: 20px 20px 0 0;

                i {
                    color: $black-color;
                    font-size: 50px;
                    position: absolute;
                    left: 20px;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
            h4 {
                font-size: 20px;
                margin-bottom: 12px;
            }
            p {
                margin-bottom: 0;
                font-size: 15px;
            }
        }
    }
    .appointment-form {
        background-color: $optional-color;
        padding: 30px;
        margin-top: 30px;

        h3 {
            font-size: 25px;
            margin-bottom: 25px;
            color: $white-color;
        }
        form {
            .form-group {
                margin-bottom: 15px;

                .form-control {
                    height: 55px;
                    padding: 20px;
                    line-height: initial;
                    color: $paragraph-color;
                    background-color: #f9f9f9;
                    border: 1px solid #f9f9f9;
                    border-radius: 10px;
                    transition: $transition;
                    font-weight: 500;
                    font-size: $font-size;
                    width: 100%;

                    &::placeholder {
                        color: $paragraph-color;
                        transition: $transition;
                    }
                    &:focus {
                        outline: 0;
                        background-color: $white-color;
                        border-color: $main-color;
                        box-shadow: none;

                        &::placeholder {
                            color: transparent;
                        }
                    }
                }
            }
            .default-btn {
                border: none;

                &:hover {
                    background-color: $main-color;
                }
            }
        }
    }
}

/*================================================
Cart Area CSS
=================================================*/
.cart-table {
    .table {
        --bs-table-bg: transparent;
    }
    table {
        margin-bottom: 0;
        
        tbody {
            tr {
                background-color: #F9F9F9 !important;
                border-bottom: 15px solid $white-color;
                border-top: none;

                &:last-child {
                    border-bottom: none;
                }
                td {
                    vertical-align: middle;
                    white-space: nowrap;
                    padding: 20px;
                    border-left: none;
                    border-right: none;

                    &.product-thumbnail {
                        a {
                            display: block;

                            img {
                                width: 100px;
                                height: 100px;
                                border: 1px solid #eeeeee;
                            }
                        }
                    }
                    &.product-name {
                        a {
                            color: $optional-color;
                            font-weight: 600;
                            display: inline-block;
                            text-decoration: none;
                            font-family: $heading-font-family;
                            font-style: italic;
                            font-size: 20px;

                            &:hover {
                                color: $main-color;
                            }
                        }
                    }
                    &.product-price {
                        .unit-amount {
                            font-weight: 600;
                            color: $paragraph-color;
                        }
                    }
                    &.product-quantity {
                        .input-counter {
                            max-width: 130px;
                            min-width: 130px;
                            text-align: center;
                            display: inline-block;
                            position: relative;    

                            span {
                                position: absolute;
                                top: 0;
                                background-color: $optional-color;
                                cursor: pointer;
                                color: $white-color;
                                width: 40px;
                                height: 100%;
                                line-height: 48px;
                                transition: $transition;
                                border-radius: 15px;

                                &.minus-btn {
                                    left: 0;
                                }
                                &.plus-btn {
                                    right: 0;
                                }
                            }
                            input {
                                height: 45px;
                                color: $optional-color;
                                outline: 0;
                                display: block;
                                border: none;
                                text-align: center;
                                width: 100%;
                                outline: 0;
                                box-shadow: none;
                                font-weight: 600;
                                border-radius: 10px;

                                &::placeholder {
                                    color: $optional-color;
                                }
                            }
                        }
                    }
                    &.product-subtotal {
                        .subtotal-amount {
                            font-weight: 600;
                            color: $paragraph-color;
                        }
                    }
                    &.remove {
                        a {
                            i {
                                color: $white-color;
                                position: relative;
                                top: -1px;
                                font-size: 20px;
                                display: block;
                                height: 30px;
                                width: 30px;
                                line-height: 30px;
                                background-color: $optional-color;
                                text-align: center;
                                border-radius: 50px;
                            }
                        }
                    }
                }
            }
        }
    }
}
.cart-buttons {
    margin-top: 30px;
    text-align: right;

    .shopping-coupon-code {
        position: relative;
        max-width: 530px;

        .form-control {
            height: 50px;
            color: $black-color;
            box-shadow: unset;
            border: 1px solid #f5f5f5;
            background-color: #f5f5f5;
            transition: $transition;
            border-radius: 5px;
            padding: 1px 0 0 15px;
            font-size: 15px;
            font-weight: 500;

            &::placeholder {
                transition: $transition;
                color: $paragraph-color;
            }
            &:focus {
                border-color: $main-color;
                background-color: transparent;

                &::placeholder {
                    color: transparent;
                }
            }
        }
        button {
            position: absolute;
            right: 0;
            top: 0;
            height: 50px;
            background: $optional-color;
            color: $white-color;
            border: none;
            padding: 0 60px 0 15px;
            line-height: 48px;
            outline: 0;
            transition: $transition;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            
            &:hover {
                background-color: $main-color;
            }
        }
    }
    .default-btn {
        background-color: $optional-color;
        color: $white-color;

        i {
            background-color: $main-color;
        }
        &:hover {
            background-color: $main-color;

            i {
                background-color: $optional-color;
                color: $white-color;
            }
        }
    }
}
.cart-totals {
    margin-top: 50px;

    h3 {
        margin-bottom: 15px;
        font-size: 25px;
    }
    ul {
        padding-left: 0;
        margin: 0 0 25px;
        list-style-type: none;
        
        li {
            border-bottom: 1px solid #eeeeee;
            padding: 20px 0;
            color: $black-color;
            overflow: hidden;
            font-weight: 600;

            b {
                font-weight: bold;
            }
            span {
                float: right;
                color: $paragraph-color;
                font-weight: 500;
            }
        }
    }
    .totals-btn {
        text-align: right;

        .default-btn {
            background-color: $optional-color;
            color: $white-color;
    
            i {
                background-color: $main-color;
            }
            &:hover {
                background-color: $main-color;
    
                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}

/*================================================
Wishlist Area CSS
=================================================*/
.wishlist-table {
    .table {
        --bs-table-bg: transparent;
    }
    table {
        margin-bottom: 0;
        
        tbody {
            tr {
                background-color: #F9F9F9 !important;
                border-bottom: 15px solid $white-color;
                border-top: none;

                &:last-child {
                    border-bottom: none;
                }
                td {
                    vertical-align: middle;
                    white-space: nowrap;
                    padding: 20px;
                    border-left: none;
                    border-right: none;

                    &.product-thumbnail {
                        a {
                            display: block;

                            img {
                                width: 100px;
                                height: 100px;
                                border: 1px solid #eeeeee;
                            }
                        }
                    }
                    &.product-name {
                        a {
                            color: $paragraph-color;
                            font-weight: 500;
                            display: inline-block;
                            text-decoration: none;
                            font-family: $heading-font-family;
                            font-size: 18px;
                            font-style: italic;

                            &:hover {
                                color: $main-color;
                            }
                        }
                    }
                    &.product-price {
                        .unit-amount {
                            font-weight: 600;
                            font-size: $font-size;
                            color: $paragraph-color;
                        }
                    }
                    &.product-btn {
                        .default-btn {
                            background-color: $optional-color;
                            color: $white-color;
                    
                            i {
                                background-color: $main-color;
                            }
                            &:hover {
                                background-color: $main-color;
                    
                                i {
                                    background-color: $optional-color;
                                    color: $white-color;
                                }
                            }
                        }
                    }
                    &.product-subtotal {
                        .remove {
                            color: $white-color;
                            float: right;
                            position: relative;
                            top: -1px;
                            font-size: 20px;
                            display: block;
                            height: 30px;
                            width: 30px;
                            line-height: 35px;
                            background-color: $optional-color;
                            text-align: center;
                            border-radius: 50px;
                        }
                    }
                }
            }
        }
    }
    .wishlist-btn {
        margin-top: 30px;
        text-align: right;

        .default-btn {
            background-color: $optional-color;
            color: $white-color;
    
            i {
                background-color: $main-color;
            }
            &:hover {
                background-color: $main-color;
    
                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}

/*================================================
Checkout Area CSS
=================================================*/
.user-actions {
    box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
    background: $white-color;
    padding: 15px 20px;
    border-top: 3px solid $main-color;
    position: relative;
    margin-bottom: 40px;

    i {
        color: $main-color;
        margin-right: 2px;
        font-size: 20px;
        position: relative;
        top: 3px;
    }
    span {
        display: inline-block;
        color: $main-color;
        font-weight: bold;
        font-size: $font-size;
        a {
            display: inline-block;
        }
    }
}
.billing-details {
    .title {
        margin-bottom: 30px;
        position: relative;
        padding-bottom: 10px;
        border-bottom: 1px solid #eeeeee;
        font-size: 25px;
        
        &::before {
            content: '';
            position: absolute;
            background: $main-color;
            bottom: -1px;
            left: 0;
            width: 50px;
            height: 1px;
        }
    }
    .form-group {
        margin-bottom: 25px;

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: $font-size;
            
            .required {
                color: red;
            }
        }
        .form-control {
            height: 50px;
            color: $black-color;
            box-shadow: unset !important;
            border: 1px solid #f5f5f5;
            background-color: #f5f5f5;
            transition: $transition;
            border-radius: 3px;
            padding: 1px 0 0 15px;
            font-size: $font-size;
            font-weight: 600;
            
            &::placeholder {
                color: $paragraph-color;
                transition: $transition;
            }
            &:focus {
                border-color: $main-color;
                background-color: transparent;
        
                &::placeholder {
                    color: transparent;
                }
            }
        }
        textarea.form-control {
            height: auto;
            padding-top: 15px;
        }
        .nice-select {
            display: block;
            width: 215px;
            float: unset;
            width: 100%;
            background: #f8f8f8;
            border-color: #eeeeee;
            color: $black-color;
            transition: $transition;
            padding: 0 0 0 12px;
            height: 55px;
            line-height: 55px;
            font-size: $font-size;
            font-weight: 600;
            
            .list {
                background-color: $white-color;
                border-radius: 5px;
                box-shadow: 0 10px 30px rgba(0,0,0,.2);
                list-style-type: none;
                border: none;
                width: 100%;
                margin-bottom: 0;
                margin-top: 0;
                padding-left: 0;
                padding-top: 10px;
                padding-bottom: 10px;
                
                .option {
                    line-height: 38px;
                    min-height: 38px;
                    color: $black-color;
                    position: relative;
                    transition: $transition;
                    padding-left: 32px;
                    padding-right: 25px;
                    font-size: $font-size;
                    font-weight: 600;
                    
                    &:hover {
                        background-color: $main-color !important;
                        color: $white-color;

                        &::before {
                            color: $white-color;
                        }
                    }
                    &.focus, &.selected.focus {
                        background-color: transparent !important;
                        color: $black-color;
                    }
                    &::before {
                        content: "\f149";
                        position: absolute;
                        left: 10px;
                        top: 0;
                        opacity: 0;
                        visibility: hidden;
                        transition: $transition;
                        color: $main-color;
                        font: {
                            family: Flaticon;
                            size: 12px;
                        };
                    }
                    &.selected {
                        &::before {
                            opacity: 1;
                            visibility: visible;
                        }
                        &:hover {
                            background-color: $main-color !important;
                            color: $white-color;

                            &::before {
                                color: $white-color;
                            }
                        }
                    }
                }
            }
            &:after {
                border-color: $black-color;
                height: 8px;
                width: 8px;
                margin-top: -5px;
            }
            &:hover {
                border-color: $main-color;
                background-color: transparent;
            }
        }
    }
    .form-check {
        margin-bottom: 20px;

        .form-check-label {
            color: $black-color;
            font-weight: 600;
        }
        label {
            position: relative;
            left: -3px;
            top: 0;

            font: {
                weight: 500;
            }
        }
    }
    .col-lg-12 {
        &:last-child {
            .form-group {
                margin-bottom: 0;
            }
        }
    }
}
.order-details {
    .title {
        margin-bottom: 30px;
        position: relative;
        padding-bottom: 10px;
        border-bottom: 1px solid #eeeeee;
        font-size: 25px;

        &::before {
            content: '';
            position: absolute;
            background: $main-color;
            bottom: -1px;
            left: 0;
            width: 50px;
            height: 1px;
        }
    }
    .order-table {
        .table {
            --bs-table-bg: transparent;
        }
        table {
            margin-bottom: 0;
    
            thead {
                tr {
                    th {
                        border-bottom-width: 0;
                        vertical-align: middle;
                        border-color: #eaedff;
                        padding: 12px 20px 10px;
                        white-space: nowrap;
                        text-transform: uppercase;
                        font-size: $font-size;
                        font-weight: bold;
                    }
                }
            }
            tbody {
                tr {
                    td {
                        vertical-align: middle;
                        color: $optional-color;
                        white-space: nowrap;
                        border-color: #eaedff;
                        font-size: $font-size;
                        font-weight: 600;
                        padding-left: 20px;
                        padding-right: 20px;
                        padding-top: 15px;
                        padding-bottom: 15px;
                        
                        &.product-name {
                            a {
                                display: inline-block;
                            }
                        }
                        &.order-subtotal, &.order-shipping , &.total-price {
                            span {
                                color: $black-color;
                                font-size: $font-size;
                                font-weight: 600;
                            }
                        }
                        &.shipping-price, &.order-subtotal-price, &.product-subtotal {
                            color: $black-color;
                            font-weight: 600;
                            font-size: $font-size;
                        }
                    }
                }
            }
        }
    }
    .payment-box {
        background-color: $white-color;
        box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.06);
        margin-top: 30px;
        padding: 30px;
        
        .payment-method {
            p {
                [type="radio"] {
                    &:checked, &:not(:checked) {
                        display: none;
                    }
                }
                [type="radio"] {
                    &:checked, &:not(:checked) {
                        + label {
                            padding-left: 27px;
                            cursor: pointer;
                            display: block;
                            color: $black-color;
                            position: relative;
                            margin-bottom: 8px;
                            font-weight: 600;

                            &::before {
                                content: '';
                                position: absolute;
                                left: 0;
                                top: 4px;
                                width: 18px;
                                height: 18px;
                                border: 1px solid #dddddd;
                                border-radius: 50%;
                                background: $white-color;
                            }
                            &::after {
                                content: '';
                                width: 12px;
                                height: 12px;
                                background: $main-color;
                                position: absolute;
                                top: 7px;
                                left: 3px;
                                border-radius: 50%;
                                transition: $transition;
                            }
                        }
                    }
                }
                [type="radio"] {
                    &:not(:checked) {
                        + label {
                            &::after {
                                opacity: 0;
                                visibility: hidden;
                                transform: scale(0);
                            }
                        }
                    }
                }
                [type="radio"] {
                    &:checked {
                        + label {
                            &::after {
                                opacity: 1;
                                visibility: visible;
                                transform: scale(1);
                            }
                        }
                    }
                }
            }
        }
        .default-btn {
            margin-top: 15px;
            box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, .2);
        }
    }
}

/*================================================
My Account Area CSS
=================================================*/
.login-form {
    padding: 35px;
    border-radius: 5px;
    background-color: $white-color;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    max-width: 650px;
    margin: auto;

    h2 {
        margin-bottom: 30px;
        font-size: 30px;
        border-bottom: 1px solid #dedddf;
        padding-bottom: 15px;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            border-bottom: 1px solid $main-color;
            height: 100%;
            width: 100px;
        }
    }
    form {
        .form-group {
            margin-bottom: 25px;

            label {
                display: block;
                margin-bottom: 5px;
                color: $paragraph-color;
                font-weight: 500;
                font-size: $font-size;
            }
            .form-control {
                height: 60px;
                padding: 0 0 0 15px;
                line-height: initial;
                color: $black-color;
                background-color: transparent;
                border: 1px solid #eeeeee;
                border-radius: 0;
                box-shadow: unset;
                transition: $transition;
                font-size: $font-size;
                font-weight: 500;

                &:focus {
                    border: 1px solid $main-color;
                    background-color: transparent;

                    &::placeholder {
                        color: transparent;
                        transition: $transition;
                    }
                }
            }
        }
        .form-check-input {
            margin-top: 2px;
        }
        .lost-your-password {
            text-align: right;

            a {
                display: inline-block;
                position: relative;
                font-weight: 500;
                font-size: 15px;
                color: $paragraph-color;

                &::before {
                    width: 100%;
                    height: 1px;
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    content: '';
                    transition: $transition;
                    background-color: #eeeeee;
                }
                &::after {
                    width: 0;
                    height: 1px;
                    position: absolute;
                    left: 0;
                    transition: $transition;
                    bottom: 0;
                    content: '';
                    background-color: $main-color;
                }
                &:hover {
                    &::before {
                        width: 0;
                    }
                    &::after {
                        width: 100%;
                    }
                }
            }
        }
        button {
            border: none;
            margin-top: 25px;
            padding: 15px 30px;
            width: 100%;
            border-radius: 5px;
            cursor: pointer;
            background-color: $main-color;
            color: $white-color;
            transition: $transition;
            font-weight: 600;

            &:hover {
                background-color: $optional-color;
            }
        }
    }
    &.mb-30 {
        margin-bottom: 30px;
    }
}
.register-form {
    padding: 35px;
    border-radius: 5px;
    background-color: $white-color;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    max-width: 650px;
    margin: auto;

    h2 {
        margin-bottom: 30px;
        font-size: 30px;
        border-bottom: 1px solid #dedddf;
        padding-bottom: 15px;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            border-bottom: 1px solid $main-color;
            height: 100%;
            width: 100px;
        }
    }
    form {
        .form-group {
            margin-bottom: 25px;

            label {
                display: block;
                margin-bottom: 5px;
                color: $paragraph-color;
                font-weight: 500;
                font-size: $font-size;
            }
            .form-control {
                height: 60px;
                padding: 0 0 0 15px;
                line-height: initial;
                color: $black-color;
                background-color: transparent;
                border: 1px solid #eeeeee;
                border-radius: 0;
                box-shadow: unset;
                transition: $transition;
                font-size: $font-size;
                font-weight: 500;

                &:focus {
                    border: 1px solid $main-color;
                    background-color: transparent;

                    &::placeholder {
                        color: transparent;
                        transition: $transition;
                    }
                }
            }
        }
        .description {
            font-style: italic;
            font-size: 13.5px;
            margin-top: -10px;
            margin-bottom: 15px;
        }
        button {
            border: none;
            padding: 15px 30px;
            width: 100%;
            border-radius: 5px;
            cursor: pointer;
            background-color: $main-color;
            color: $white-color;
            transition: $transition;
            font-weight: 600;

            &:hover {
                background-color: $optional-color;
            }
        }
    }
}

/*================================================
Products Details Area CSS
=================================================*/
.products-details-area {
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.products-details-slides {
    position: relative;
    
    .products-feedback {
        .item {
            .products-image {
                img {
                    border-radius: 0 100px 0 0;
                }
            }
        }
    }
    .products-thumbnails {
        position: relative;
        z-index: 1;
        padding-left: 30px;
        padding-top: 50px;

        .item {
            position: relative;
            
            .products-image {
                cursor: pointer;
                text-align: center;

                img {
                    padding: 5px;
                    border-radius: 0 30px 0 0;
                }
            }
        }
    }
    .next-arrow, .prev-arrow {
        position: absolute;
        cursor: pointer;
        bottom: 45px;
        background: $main-color;
        border: none;
        color: $black-color;
        z-index: 9;
        border-radius: 10px 10px 0 10px;
        outline: 0 !important;
        transition: $transition;
        font-size: 25px;
        display: inline-block;
        height: 50px;
        width: 50px;
        line-height: 52px;
        opacity: 0;
        visibility: hidden;
    }
    .next-arrow {
        right: -35px;
    }
    .prev-arrow {
        display: none !important;
    }
    &:hover {
        .next-arrow, .prev-arrow {
            opacity: 1;
            visibility: visible;
        }
    }
    .slick-list {
        padding: {
            left: 0 !important;
            right: 0 !important;
        }
    }
}
.products-details-desc {
    padding-left: 30px;
    
    h3 {
        margin-bottom: 20px;
        font-size: 38px;
    }
    .price {
        margin-bottom: 15px;
        color: $optional-color;
        font-size: 25px;
        font-weight: bold;
    }
    .products-review {
        margin-bottom: 18px;
        position: relative;

        .rating {
            display: inline-block;
            padding-right: 5px;
            font-size: 18px;
            position: absolute;
            bottom: 15px;
            right: 0;

            i {
                color: #ffba0a;
                display: inline-block;
                margin-right: -2px;
            }
            .rating-count {
                display: inline-block;
                color: $black-color;
                line-height: initial;
                position: relative;
                top: -3px;
                font-weight: 500;
    
                &:hover {
                    color: $main-color;
                    border-color: $main-color;
                }
            }
        }
    }
    .products-add-to-cart {
        margin-top: 25px;

        .input-counter {
            max-width: 130px;
            min-width: 130px;
            margin-left: 10px;
            text-align: center;
            display: inline-block;
            position: relative;    

            span {
                position: absolute;
                top: 10px;
                background-color: $main-color;
                cursor: pointer;
                color: $white-color;
                width: 30px;
                height: 30px;
                line-height: 35px;
                transition: $transition;
                font-size: 25px;
                border-radius: 5px;

                &.minus-btn {
                    left: 10px;
                }
                &.plus-btn {
                    right: 10px;
                }
                &:hover {
                    color: $white-color;
                }
            }
            input {
                height: 50px;
                color: $white-color;
                outline: 0;
                display: block;
                border: none;
                background-color: $optional-color;
                text-align: center;
                width: 100%;
                font-size: $font-size;
                font-weight: 600;    
                border-radius: 10px 10px 0 10px;
                
                &::placeholder {
                    color: $black-color;
                }
            }
        }
        .default-btn {
            top: -2px;
            padding-top: 12.5px;
            padding-bottom: 12.5px;
            border: none;
            background-color: $optional-color;
            color: $white-color;

            i {
                background-color: $main-color;
            }
            &:hover {
                background-color: $main-color;

                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}
.products-details-tabs {
    margin-top: 50px;

    .nav {
        margin-bottom: 30px;
        list-style-type: none;
        display: block;

        .nav-item {
            display: inline-block;
            margin-right: 10px;

            &:last-child {
                margin-right: 0;
            }
            .nav-link {
                border: none;
                padding: 20px 70px;
                background-color: #E6E6E6;
                position: relative;
                font-size: $font-size;
                font-weight: bold;
                color: $black-color;
                transition: $transition;
                border-radius: 0;
                
                &:hover, &.active {
                    background-color: $main-color;
                    color: $white-color;
                }
            }
        }
    }
    .nav-tabs {
        border-bottom: none;
    }
    .tab-content {
        .tab-pane {
            .products-reviews {
                position: relative;

                .review-content {
                    position: relative;
                    padding: 30px 30px 30px 170px;
                    margin-bottom: 30px;
                    background-color: #f9f9f9;
                    border-radius: 5px;

                    img {
                        position: absolute;
                        left: 25px;
                        top: 28px;
                        height: 120px;
                        width: 120px;
                        border-radius: 5px;
                    }
                    h3 {
                        font-size: 20px;
                        font-weight: 600;
                        margin-bottom: 8px;
                        font-family: $main-font-family;
                    }
                    span {
                        color: #8D8D8D;
                        font-size: 14px;
                        font-style: italic;
                        display: inline-block;
                        margin-bottom: 12px;
                    }
                    p {
                        margin-bottom: 0;
                    }
                    .rating {
                        display: inline-block;
                        font-size: 25px;
                        position: absolute;
                        right: 30px;
                        top: 30px;
        
                        i {
                            color: #ffba0a;
                        }
                        a {
                            font-size: $font-size;
                            color: $paragraph-color;
                        }
                    }
                }
            }
            .products-review-form {
                margin-top: 45px;

                .review-form {
                    position: relative;

                    h3 {
                        font-size: 25px;
                        margin-bottom: 20px;
                    }
                    form {
                        .form-group {
                            margin-bottom: 15px;

                            .form-control {
                                height: 60px;
                                color: $black-color;
                                box-shadow: unset;
                                border: 1px solid #f9f9f9;
                                background-color: #f9f9f9;
                                transition: $transition;
                                border-radius: 15px;
                                padding: 1px 0 0 15px;
                                font-size: $font-size;
                                font-weight: 600;
                                
                                &::placeholder {
                                    color: #7e7e7e;
                                    transition: $transition;
                                }
                                &:focus {
                                    border-color: $main-color;
                                    background-color: transparent;
                            
                                    &::placeholder {
                                        color: transparent;
                                    }
                                }
                            }
                            textarea {
                                &.form-control {
                                    height: auto;
                                    padding-top: 15px;
                                }
                            }
                        }
                        .form-check {
                            margin-bottom: 15px;
                            margin-top: 5px;

                            label {
                                font-size: 16px;
                                font-weight: 500;
                            }
                        }
                        .default-btn {
                            color: $white-color;
                            cursor: pointer;
                            position: relative;
                            z-index: 1;
                            border: none;
                        }
                    }
                    .rating {
                        display: inline-block;
                        font-size: 25px;
                        position: absolute;
                        right: 0;
                        top: 0;
        
                        i {
                            color: #ffba0a;
                        }
                        a {
                            font-size: $font-size;
                            color: $paragraph-color;
                        }
                    }
                }
            }
        }
    }
}
.products-details-shape {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    z-index: -1;
    width: 100%;
    opacity: 50%;

    img {
        width: 100%;
    }
}

/*================================================
Blog Details Area CSS
=================================================*/
.blog-details-desc {
    .article-content {
        .title-box {
            text-align: center;
            max-width: 920px;
            margin: auto;

            h2 {
                font-size: 35px;
                margin-bottom: 20px;
                line-height: 1.5;
            }
            .entry-meta {
                margin-bottom: 30px;
    
                ul {
                    padding-left: 0;
                    margin-bottom: 0;
                    list-style-type: none;
        
                    li {
                        position: relative;
                        display: inline-block;
                        color: $black-color;
                        margin-right: 20px;
                        padding-left: 25px;
    
                        a {
                            display: inline-block;
                            color: $paragraph-color;
        
                            &:hover {
                                color: $main-color;
                            }
                        }
                        i {
                            color: $main-color;
                            position: absolute;
                            left: 0;
                            top: 2px;
                        }
                        &::before {
                            content: '';
                            position: absolute;
                            top: 12px;
                            right: -15px;
                            width: 6px;
                            height: 1px;
                            background: $main-color;
                        }
                        &:last-child {
                            margin-right: 0;
    
                            &::before {
                                display: none;
                            }
                        }
                    }
                }
            }
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 25px;
        }
        blockquote, .blockquote {
            overflow: hidden;
            background-color: #fafafa;
            padding: 30px !important;
            position: relative;
            z-index: 1;
            margin-top: 20px;
            margin-bottom: 20px;
            
            p {
                color: $black-color;
                line-height: 1.5;
                margin-bottom: 0;
                font-style: italic;
                font-weight: 600;
                font-size: 18px !important;
            }
            &::before {
                color: $main-color;
                content: "\ee81";
                position: absolute;
                left: 50px;
                top: -50px;
                z-index: -1;
                opacity: 30%;
                font-size: 130px;
                font-family: "Boxicons";
                font-weight: 900;
            }
        }
        .wp-block-gallery {
            &.columns-3 {
                padding-left: 0;
                list-style-type: none;
                display: flex;
                flex-wrap: wrap;
                margin-left: -10px;
                margin-right: -10px;
                margin-top: 30px;
                margin-bottom: 30px;
                
                li {
                    flex: 0 0 33.3333%;
                    max-width: 33.3333%;
                    padding-left: 10px;
                    padding-right: 10px;
                    
                    figure {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    .article-footer {
        display: flex;
        flex-wrap: wrap;
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid #eeeeee;

        .article-tags {
            flex: 0 0 50%;
            max-width: 50%;

            span {
                font-size: $font-size; 
                font-weight: bold;
                margin-right: 5px;
            }
            a {
                display: inline-block;
                background-color: #E6E6E6;
                color: $paragraph-color;
                padding: 10px 25px;
                border-radius: 10px;
                margin-right: 5px;
                font-weight: 600;

                &:last-child {
                    margin-right: 0;
                }
                &:hover {
                    background-color: $main-color;
                    color: $white-color;
                }
            }
        }
        .article-share {
            flex: 0 0 50%;
            max-width: 50%;
            text-align: right;
            margin-top: 10px;

            span {
               font-size: $font-size; 
               font-weight: bold;
               margin-right: 5px;
            }
            a {
                display: inline-block;
                color: $main-color;
                width: 32px;
                height: 32px;
                line-height: 35px;
                border-radius: 50%;
                background-color: #eeeef0;
                text-align: center;
                font-size: 14px;

                &:hover {
                    color: $white-color;
                    background-color: $main-color;
                    transform: translateY(-2px);
                }
            }
        }
    }
}
.psylo-post-navigation {
    overflow: hidden;
    margin-top: 35px;
    padding-top: 25px;
    padding-bottom: 25px;
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #eeeeee;

    .prev-link-wrapper {
        float: left;

        a {
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -moz-box-align: center;
            -ms-flex-align: center;
            align-items: center;

            &:hover {
                .image-prev {
                    &::after {
                        opacity: 1;
                        visibility: visible;
                    }
                    .post-nav-title {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }
        }
        .image-prev {
            display: inline-block;
            min-width: 100px;
            min-height: 100px;
            border-radius: 5px;
            overflow: hidden;
            vertical-align: top;
            margin-right: 20px;
            position: relative;
            transition: $transition;

            img {
                border-radius: 50%;
                width: 100px;
                height: 100px;
            }
            &::after {
                display: block;
                content: '';
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border-radius: 50%;
                opacity: 0;
                background-color: $main-color;
                visibility: hidden;
                transition: $transition;
            }
            .post-nav-title {
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                right: 0;
                margin: 0 auto;
                text-align: center;
                text-transform: uppercase;
                z-index: 2;
                color: $white-color;
                opacity: 0;
                visibility: hidden;
                transition: $transition;
                font-size: $font-size;
                font-weight: bold;
            }
        }
        .prev-link-info-wrapper {
            color: $black-color;
        }
        .prev-title {
            max-width: 360px;
            display: inline-block;
            font-weight: bold;
            font-size: 18px;
        }
        .default-btn {
            display: inline-block !important;
            margin-top: 25px;
            background-color: $optional-color;
            color: $white-color;

            i {
                background-color: $main-color;
            }
            &:hover {
                background-color: $main-color;

                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
    .next-link-wrapper {
        float: right;
        text-align: right;

        a {
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -moz-box-align: center;
            -ms-flex-align: center;
            align-items: center;

            &:hover {
                .image-next {
                    &::after {
                        opacity: 1;
                        visibility: visible;
                    }
                    .post-nav-title {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }
        }
        .image-next {
            display: inline-block;
            min-width: 100px;
            min-height: 100px;
            border-radius: 5px;
            overflow: hidden;
            vertical-align: top;
            margin-left: 20px;
            position: relative;
            transition: $transition;

            img {
                border-radius: 50%;
                width: 100px;
                height: 100px;
            }
            &::after {
                display: block;
                content: '';
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border-radius: 50%;
                opacity: 0;
                background-color: $main-color;
                visibility: hidden;
                transition: $transition;
            }
            .post-nav-title {
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                right: 0;
                margin: 0 auto;
                text-align: center;
                text-transform: uppercase;
                z-index: 2;
                color: $white-color;
                opacity: 0;
                visibility: hidden;
                transition: $transition;
                font-size: $font-size;
                font-weight: bold;
            }
        }
        .next-link-info-wrapper {
            color: $black-color;
        }
        .next-title {
            max-width: 360px;
            display: inline-block;
            font-weight: bold;
            font-size: 18px;
        }
        .default-btn {
            display: inline-block !important;
            margin-top: 25px;
            background-color: $optional-color;
            color: $white-color;

            i {
                background-color: $main-color;
            }
            &:hover {
                background-color: $main-color;

                i {
                    background-color: $optional-color;
                    color: $white-color;
                }
            }
        }
    }
}
.comments-area {
    margin-top: 30px;

    .comments-title {
        margin-bottom: 30px;
        line-height: initial;
        font-size: 30px;
    }
    ol, ul {
        padding: 0;
        margin: 0;
        list-style-type: none;
    }
    .comment-list {
        padding: 0;
        margin: 0;
        list-style-type: none;
    }
    .children {
        margin-left: 50px;
    }
    .comment-body {
        padding: 30px 30px 30px 175px;
        color: $black-color;
        margin-bottom: 40px;
        position: relative;
        background-color: #f9f9f9;
        border-radius: 5px;

        .reply {
            position: absolute;
            top: 30px;
            right: 30px;
            z-index: 1;

            a {
                border: 1px dashed #ded9d9;
                color: $optional-color;
                display: inline-block;
                padding: 8px 20px 8px;
                border-radius: 30px;
                font-size: 14px;
                font-weight: 600;
                transition: $transition;

                &:hover {
                    background-color: $main-color;
                    border: 1px solid $main-color;
                    color: $white-color;
                }
            }
        }
    }
    .comment-author {
        font-size: 15px;
        margin-bottom: 0.3em;
        position: relative;

        .avatar {
            height: 120px;
            left: -140px;
            position: absolute;
            width: 120px;
            border-radius: 5px;
        }
        .fn {
            font-weight: 600;
            font-size: 20px;
        }
    }
    .comment-metadata {
        margin-bottom: .8em;
        color: $paragraph-color;
        letter-spacing: 0.01em;
        font-size: 14px;
        font-weight: 400;
        a {
            color: $paragraph-color;

            &:hover {
                color: $main-color;
            }
        }
    }
    .comment-respond {
        margin-top: 30px;

        .comment-reply-title {
            margin-bottom: 30px;
            font-size: 30px;
        }
        .comment-form {
            .form-group {
                margin-bottom: 15px;
        
                .form-control {
                    display: block;
                    width: 100%;
                    height: 70px;
                    outline: 0;
                    background-color: #f9f9f9;
                    border: 1px solid #f9f9f9;
                    border-radius: 15px;
                    box-shadow: none;
                    padding: 10px 25px;
                    transition: $transition;
                    font-size: $font-size;
                    font-weight: $font-size;
                    font-weight: 500;
        
                    &::placeholder {
                        color: $paragraph-color;
                        transition: $transition;
                    }
                    &:focus {
                        outline: 0;
                        background-color: $white-color;
                        border-color: $main-color;
                        box-shadow: none;
        
                        &::placeholder {
                            color: transparent;
                        }
                    }
                }
                textarea.form-control {
                    height: 150px;
                    padding: 25px;
                    line-height: 1.5rem;
                }
            }
            .default-btn {
                border: none;
                margin-top: 10px;
            }
        }
    }
}

/*================================================
Purchase Guide Area CSS
=================================================*/
.purchase-guide-content {
    img {
        border-radius: 5px;
        display: block;
        width: 100%;
        margin-bottom: 30px;
    }
    h3 {
        margin-top: 25px;
        margin-bottom: 15px;
        font-size: 25px;

        span {
            font-family: $main-font-family;
        }
    }
    .blockquote, blockquote {
        margin-bottom: 30px;
        background-color: #e6f2f5;
        text-align: left !important;
        padding: 25px !important;

        p {
            margin-bottom: 0;
            line-height: 1.8;
            font-size: $font-size !important;
            font-weight: 500;
        }
        &::after {
            display: none;
        }
        &::before {
            display: none;
        }
    }
    ol, ul {
        margin-top: 20px;

        li {
            margin-bottom: 10px;
            color: $optional-color;
            line-height: 1.8;
            font-weight: 500;
            font-size: $font-size;
        }
    }
}
.sidebar-information {
    padding-left: 0;
    margin-bottom: 0;
    list-style-type: none;

    li {
        margin-bottom: -1px;

        a {
            position: relative;
            display: block;
            padding: 25px 25px 25px 40px;
            color: $optional-color;
            border: 1px solid #eeeeee;
            font-size: $font-size;
            font-weight: bold;
            
            &::before {
                width: 5px;
                height: 5px;
                transition: $transition;
                background-color: $optional-color;
                content: '';
                position: absolute;
                left: 25px;
                top: 50%;
                transform: translateY(-50%);
                border-radius: 50%;
            }
            &:hover, &.active {
                border-color: $main-color;
                background-color: $main-color;
                color: $white-color;

                &::before {
                    background-color: $white-color;
                }
            }
        }
        &.active {
            a {
                border-color: $main-color;
                background-color: $main-color;
                color: $white-color;

                &::before {
                    background-color: $white-color;
                }
            }
        }
    }
}

/*================================================
Book Online Area CSS
=================================================*/
.book-online-area {
    background-color: #f9f9f9;
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.book-online-form {
    padding: 90px 0 100px;
    border-radius: 10px;

    &.bg-top {
        padding: 30px 0 30px;
    }
    h3 {
        font-size: 30px;
        margin-bottom: 30px;
    }
    form {
        .form-group {
            margin-bottom: 15px;

            .form-control {
                height: 60px;
                padding: 20px;
                line-height: initial;
                color: $paragraph-color;
                background-color: $white-color;
                box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
                border: 1px solid $white-color;
                border-radius: 10px;
                transition: $transition;
                font-weight: 500;
                font-size: $font-size;
                width: 100%;
                
                &:focus {
                    border: 1px solid $main-color;
                    background-color: transparent;
                    box-shadow: unset;
                    outline: 0;
                }
            }
        }
        .nice-select {
            height: 60px;
            width: 100%;
            line-height: 60px;
            padding-left: 20px;
            padding-right: 20px;
            color: $paragraph-color;
            background-color: $white-color;
            box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
            border: 1px solid $white-color;
            border-radius: 10px;
            transition: $transition;
            margin-bottom: 15px;
            font-weight: 500;
            font-size: $font-size;
    
            &:focus {
                border: 1px solid $main-color;
                background-color: transparent;
                box-shadow: unset;
            }
            .list {
                background-color: $white-color;
                box-shadow: 0px 0px 29px 0px rgba(102, 102, 102, 0.1);
                border-radius: 0;
                margin-top: 0;
                margin-bottom: 0;
                width: 100%;
                padding-top: 10px;
                padding-bottom: 10px;
                height: 215px;
                overflow-y: scroll;
                
                .option {
                    transition: $transition;
                    color: $black-color;
                    padding-left: 20px;
                    padding-right: 20px;
                    font-size: $font-size;
                    font-weight: 500;
    
                    &:hover {
                        background-color: $main-color !important;
                        color: $white-color;
                    }
                    &.selected {
                        background-color: transparent;
                        font-weight: 600;
                    }
                }
            }
            &::after {
                height: 8px;
                width: 8px;
                border-color: $main-color;
                right: 20px;
            }
        }
        .book-online-btn {
            margin-top: 20px;

            .default-btn {
                border: none;
                cursor: pointer;
            }
        }
    }
}
.book-online-image {
    background-image: url(../../assets/images/book-online.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%
}
.book-main-shape {
    position: absolute;
    bottom: 0;
    z-index: -1;
    left: 0;
    opacity: 50%;
    width: 100%;

    img {
        width: 100%;
    }
}

/*================================================
Gallery Area CSS
=================================================*/
.single-gallery-item {
    position: relative;
    margin-bottom: 30px;
    z-index: 1;
    border-radius: 5px;
    overflow: hidden;
    cursor: zoom-in;

    a {
        display: block;
        border-radius: 5px;
        
        img {
            transition: $transition;
        }
    }
    &:hover {
        a {
            img {
                transform: scale(1.1);
            }
        }
    }
}

/*================================================
Book Appointment Area CSS
=================================================*/
.book-appointment-form {
    text-align: center;
    max-width: 1050px;
    margin: auto;
    background-color: $white-color;
    box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
    padding: 45px 30px;
    border-radius: 15px;

    h3 {
        font-size: 35px;
        margin-bottom: 30px;
    }
    .form-group {
        margin-bottom: 15px;

        .form-control {
            display: block;
            width: 100%;
            height: 70px;
            outline: 0;
            background-color: #f9f9f9;
            border: 1px solid #f9f9f9;
            border-radius: 15px;
            box-shadow: none;
            padding: 10px 25px;
            transition: $transition;
            font-size: $font-size;
            font-weight: $font-size;
            font-weight: 600;

            &::placeholder {
                color: $paragraph-color;
                transition: $transition;
            }
            &:focus {
                outline: 0;
                background-color: $white-color;
                border-color: $main-color;
                box-shadow: none;

                &::placeholder {
                    color: transparent;
                }
            }
        }
        textarea {
            &.form-control {
                height: 120px;
                padding: 25px;
                line-height: 1.5rem;
            }
        }
    }
    .default-btn {
        border: none;
        margin-top: 10px;
    }
}

/*================================================
404 Error Area CSS
=================================================*/
.error-content {
    text-align: center;
    margin: 0 auto;
    max-width: 750px;
    
    h3 {
        font-size: 38px;
        margin-top: 45px;
        margin-bottom: 20px;
    }
    p {
        max-width: 520px;
        margin: 0 auto 25px;
        font-size: 15px;
    } 
}

/*================================================
Privacy Policy Area CSS
=================================================*/
.privacy-policy-content {
    img {
        border-radius: 5px;
        display: block;
        width: 100%;
        margin-bottom: 30px;
    }
    h3 {
        margin-top: 25px;
        margin-bottom: 15px;
        font-size: 25px;

        span {
            font-family: $main-font-family;
        }
    }
    .blockquote, blockquote {
        margin-bottom: 30px;
        background-color: #e6f2f5;
        text-align: left !important;
        padding: 25px !important;

        p {
            margin-bottom: 0;
            line-height: 1.8;
            font-size: $font-size !important;
            font-weight: 500;
        }
        &::after {
            display: none;
        }
        &::before {
            display: none;
        }
    }
    ol, ul {
        margin-top: 20px;

        li {
            margin-bottom: 10px;
            color: $paragraph-color;
            line-height: 1.8;
            font-weight: 500;
            font-size: $font-size;
        }
    }
}

/*================================================
Terms of Service Area CSS
=================================================*/
.terms-of-service-content {
    img {
        border-radius: 5px;
        display: block;
        width: 100%;
        margin-bottom: 30px;
    }
    h3 {
        margin-top: 25px;
        margin-bottom: 15px;
        font-size: 25px;

        span {
            font-family: $main-font-family;
        }
    }
    .blockquote, blockquote {
        margin-bottom: 30px;
        background-color: #e6f2f5;
        text-align: left !important;
        padding: 25px !important;

        p {
            margin-bottom: 0;
            line-height: 1.8;
            font-size: $font-size !important;
            font-weight: 500;
        }
        &::after {
            display: none;
        }
        &::before {
            display: none;
        }
    }
    ol, ul {
        margin-top: 20px;

        li {
            margin-bottom: 10px;
            color: $paragraph-color;
            line-height: 1.8;
            font-weight: 500;
            font-size: $font-size;
        }
    }
}

/*================================================
Coming Soon Area CSS
=================================================*/
.coming-soon-area {
    height: 100vh;
    background-image: url(../../assets/images/coming-soon-bg.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}
.coming-soon-content {
    max-width: 750px;
    background: $white-color;
    border-radius: 10px;
    overflow: hidden;
    text-align: center;
    padding: 45px 30px;
    box-shadow: 1px 5px 24px 0 rgba(68,102,242,.05);
    margin: auto;
    
    h1 {
        font-size: 45px;
        margin-bottom: 20px;

        a {
            color: $main-color;
        }
    }
    h2 {
        font-size: 30px;
        margin-bottom: 0;
    }
    #timer {
        margin-top: 30px;

        div {
            background-color: $main-color;
            color: $white-color;
            width: 130px;
            height: 130px;
            border-radius: 50%;
            font-size: 40px;
            font-weight: bold;
            margin-left: 5px;
            margin-right: 5px;
            
            span {
                display: block;
                margin-top: -2px;
                font-size: 15px;
                font-weight: 500;
            }
        }
    }
    .newsletter-form {
        margin-top: 45px;
        background-color: $white-color;
        box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
        padding: 30px;
        
        .form-group {
            margin-bottom: 15px;
            width: 100%;
            
            .input-newsletter {
                border: none;
                border: 1px solid #f9f9f9;
                background-color: #f9f9f9;
                padding: 20px;
                color: $black-color;
                height: 70px;
                display: block;
                width: 100%;
                transition: $transition;
                font-size: $font-size;
                font-weight: 500;
                border-radius: 15px;

                &::placeholder {
                    color: $paragraph-color;
                    transition: $transition;
                }
                &:focus {  
                    box-shadow: none;   
                    outline: 0;
                    background-color: transparent;   
                    border: 1px solid $main-color;

                    &::placeholder {
                        color: transparent;
                    }
                }
            }
        }
        .default-btn {
            width: 100%;
            border-radius: 15px;
            border: none;
            position: relative;
            top: 0;
            left: 0;
            padding: 20px;
        }
        .validation-danger {
            margin-top: 15px;
            color: red;
        }
    }
}

/*================================================
Contact Info Area CSS
=================================================*/
.contact-info-box {
    text-align: center;
    transition: $transition;
    box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
    background: $main-color;
    padding: 35px 35px 30px 35px;
    margin-bottom: 30px;
    position: relative;
    border-radius: 50px 50px 0 50px;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 0;
        background: $optional-color;
        z-index: -1;
        transition: $transition;
        border-radius: 50px 50px 0 50px;
    }
    .icon {
        margin-bottom: 22px;
        background-color: $white-color;
        padding: 30px;
        position: relative;
        text-align: left;
        border-radius: 20px 20px 0 0;

        i {
            font-size: 35px;
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
        }
        h3 {
            margin-bottom: 0;
            transition: $transition;
            font-size: 25px;
        }
    }
    p {
        margin-bottom: 2px;
        transition: $transition;
        color: $optional-color;

        a {
            display: inline-block;
            color: $optional-color;

            &:hover {
                color: $main-color;
            }
        }
        i {
            color: $white-color;
            margin-right: 5px;
        }
        &:last-child {
            margin-bottom: 0;
        }
    }
    &:hover {
        transform: translateY(-10px);

        &::before {
            height: 100%;
        }
        p {
            color: $white-color;
            
            a {
                color: $white-color;
            }
        }
    }
}

/*================================================
Contact Area CSS
=================================================*/
.contact-area {
    background-color: #f9f9f9;
}
.contact-form {
    padding: 90px 0 100px;

    h3 {
        font-size: 45px;
        margin-bottom: 30px;

        span {
            font-weight: 500;
        }
    }
}
#contactForm {
    .form-group {
        margin-bottom: 15px;

        .form-control {
            height: 60px;
            padding: 20px;
            line-height: initial;
            color: $paragraph-color;
            background-color: $white-color;
            box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
            border: 1px solid $white-color;
            border-radius: 10px;
            transition: $transition;
            font-weight: 500;
            font-size: $font-size;
            width: 100%;

            &::placeholder {
                color: $paragraph-color;
                transition: $transition;
            }
            &:focus {
                outline: 0;
                background-color: $white-color;
                border-color: $main-color;
                box-shadow: none;

                &::placeholder {
                    color: transparent;
                }
            }
        }
        textarea.form-control {
            height: auto;
            padding: 20px;
            line-height: 1.5rem;
        }
        .help-block {
            &.with-errors {
                ul {
                    color: red;
                    margin-bottom: 0;
                    margin-top: 10px;

                    li {
                        font-size: 14px;
                        text-align: left;
                    }
                }
            }
        }
    }
    .form-check {
        margin-bottom: 20px;
        margin-top: 25px;

        label {
            font-size: $font-size;
            font-weight: 600;
        }
    }
    #msgSubmit {
        margin: 0;
        font-size: 1.3rem;

        &.text-danger, &.text-success {
            margin-top: 20px;
            font-size: 20px;
            font-family: $main-font-family;
        }
    }
    .default-btn {
        border: none;
        margin-top: 5px;
    }
}
.contact-image {
    background-image: url(../../assets/images/contact.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%
}

/*================================================
Pagination Area CSS
=================================================*/
.pagination-area {
    margin-top: 20px;
    text-align: center;

    .page-numbers {
        width: 40px;
        height: 40px;
        background-color: $white-color;
        box-shadow: 0 0px 30px 5px #e4e4ee;
        color: $black-color;
        text-align: center;
        display: inline-block;
        border-radius: 3px;
        line-height: 41px;
        position: relative;
        margin-left: 5px;
        margin-right: 5px;
        font-size: $font-size;
        font-weight: bold;
        &:hover, &.current {
            color: $white-color;
            background-color: $main-color;
        }
        i {
            position: relative;
            top: 1.5px;
        }
    }
}

/*================================================
Subscribe Area CSS
=================================================*/
.subscribe-inner-box {
    background-image: url(../../assets/images/subscribe-bg.jpg);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    padding-top: 100px;
    padding-bottom: 100px;
    border-radius: 15px;
    position: relative;
    z-index: 1;

    &::before {
        position: absolute;
        content: "";
        height: 100%;
        width: 100%;
        background-color: $white-color;
        left: 0;
        right: 0;
        top: 0;
        z-index: -1;
        opacity: .50;
    }
    .subscribe-content {
        text-align: center;
        max-width: 835px;
        margin: auto;
    
        h2 {
            margin-bottom: 25px;
            font-size: 45px;
            margin-top: -5px;
        }
        p {
            color: $black-color;
        }
        .newsletter-form {
            position: relative;
            max-width: 650px;
            margin: 30px auto 0;

            .input-newsletter {
                display: block;
                width: 100%;
                background-color: $white-color;
                border: none;
                height: 65px;
                padding: 0 0 0 25px;
                border-radius: 15px;
                padding-top: 0;
                outline: 0;
                color: $black-color;
                font-weight: 500;
                transition: $transition;

                &::placeholder {
                    color: $paragraph-color;
                    transition: $transition;
                }
                &:focus {
                    &::placeholder {
                        color: transparent;
                    }
                }
            }
            button {
                position: relative;
                background: $black-color;
                color: $white-color;
                border: none;
                height: 65px;
                padding: 0 55px 0 25px;
                border-radius: 10px 10px 0 10px;
                transition: $transition;
                font-size: 15px;
                font-weight: 700;
                width: 100%;

                &:hover {
                    background-color: $main-color;

                    i {
                        background-color: $black-color;
                    }
                }
                i {
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    text-align: center;
                    display: inline-block;
                    height: 38px;
                    width: 38px;
                    line-height: 38px;
                    color: $white-color;
                    border-radius: 10px 10px 0 10px;
                    background-color: $main-color;
                    transition: $transition;
                }
            }
            #validator-newsletter {
                color: red;
                font-weight: 500;
            }
        }
    }
}
.subscribe-area {
    &.bg-top {
        margin-bottom: -185px;
        position: relative;
        z-index: 1;
    }
}

/*================================================
Footer Area CSS
=================================================*/
.footer-area {
    background-color: #000000;
    position: relative;

    &::before {
        display: none;
    }
    &.bg-top {
        padding-top: 290px;
    }
}
.single-footer-widget {
    margin-bottom: 30px;
    position: relative;
    z-index: 1;

    .widget-logo {
        margin-bottom: 30px;
    }
    p {
        color: $white-color;
        font-size: 15px;
    }
    .widget-share {
        margin-top: 25px;

        a {
            margin: 0 5px;
            i {
                display: inline-block;
                height: 35px;
                width: 35px;
                line-height: 35px;
                font-size: $font-size;
                background-color: transparent;
                color: $white-color;
                border: 1px solid $white-color;
                text-align: center;
                border-radius: 50px;
                transition: $transition;

                &:hover {
                    background-color: $main-color;
                    border: 1px solid $main-color;
                    transform: translateY(-5px);
                }
            }
        }
    }
    h3 {
        font-size: 25px;
        margin-bottom: 30px;
        color: $main-color;
        font-weight: 600;
    }
    .quick-links {
        padding-left: 0;
        margin-bottom: 0;
        list-style-type: none;

        li {
            margin-bottom: 18px;
            color: $white-color;
            font-size: 15px;

            a {
                display: inline-block;
                color: $white-color;
                font-weight: 400;
                position: relative;
                
                i {
                    color: $main-color;
                    font-size: 14px;
                    margin-right: 5px;
                }
                &:hover {
                    color: $main-color;

                    &::before {
                        width: 100%;
                    }
                }
                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 0;
                    height: 1px;
                    transition: $transition;
                    background-color: $main-color;
                }
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    .footer-contact-info {
        padding-left: 0;
        margin-bottom: 0;
        list-style-type: none;

        li {
            margin-bottom: 20px;
            color: $white-color;
            position: relative;
            padding-left: 40px;
            font-size: 15px;

            i {
                position: absolute;
                left: 0;
                top: 5px;
                font-size: 25px;
                color: $main-color;
            }
            span {
                display: block;
                font-weight: 500;
                margin-bottom: 10px;
                color: $main-color;
                font-size: $font-size;
            }
            a {
                display: inline-block;
                color: $white-color;
                font-weight: 400;
                position: relative;

                &:hover {
                    color: $main-color;

                    &::before {
                        width: 100%;
                    }
                }
                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 0;
                    height: 1px;
                    transition: $transition;
                    background-color: $main-color;
                }
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

/*================================================
Copy Right Area CSS
=================================================*/
.copyright-area {
    padding-top: 30px;
    padding-bottom: 30px;
    background-color: $main-color;

    .copyright-area-content {
        text-align: center;

        p {
            color: $black-color;
            font-size: $font-size;
            font-weight: 500;

            a {
                display: inline-block;
                font-weight: bold;
                color: $white-color;
                transition: $transition;

                &:hover {
                    color: $optional-color;
                }
            }
        }
    }
}

/*================================================
Go Top CSS
=================================================*/
.go-top {
    position: fixed;
    cursor: pointer;
    bottom: -100px;
    right: 20px;
    color: $white-color;
    background-color: $black-color;
    z-index: 4;
    width: 35px;
    text-align: center;
    height: 35px;
    opacity: 0;
    visibility: hidden;
    border-radius: 50%;
    transition: .9s;
    overflow: hidden;
    box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.1);

    i {
        position: absolute;
        right: 0;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
        font-size: 20px;
        margin-left: auto;
        margin-right: auto;
    }
    &.active {
        opacity: 1;
        visibility: visible;
        bottom: 20px;
    }
    &:hover {
        background-color: $main-color;
        color: $white-color;
        transition: $transition;
        box-shadow: 0 4px 6px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08);
        transform: translateY(-5px);
    }
}

/*=================================
Buy Now Btn
====================================*/
.buy-now-btn {
    right: 20px;
    z-index: 99;
    top: 50%;
    position: fixed;
    transform: translateY(-50%);
    border-radius: 30px;
    display: inline-block;
    color: $white-color !important;
    background-color: #82b440;
    padding: 3.5px 10px;
    font-size: 12px;
    font-weight: 500;
    animation: {
        name: tada;
        duration: 5s;
        fill-mode: both;
        iteration-count: infinite;
    };
    &:hover {
        background-color: $main-color;
        color: $white-color !important;
    }
}
@keyframes tada {
    0% {
        transform: scale3d(1, 1, 1);
    }
    10%, 20% {
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
    }
    30%, 50%, 70%, 90% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }
    40%, 60%, 80% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }
    100% {
        transform: scale3d(1, 1, 1);
    }
}