.article-content figure{
    margin: 0;
    padding: 0;
}

.pt-288 {
    padding-top: 288px;
}

@media (max-width: 768px) {
    .pt-288 {
        padding-top: 100px;
    }
}

.login-form form button {
    margin-top: 0;
}

.color-primary{
    color: #000000 !important;
}

.services-details-info .choose-us-services .choose-us-content .icon img {
    color: #212529;
    width: 50px;
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.pt-140 {
    padding-top: 140px;
    padding-bottom: 30px;
}

@media (max-width: 768px) {
    .pt-140 {
        padding-top: 80px;
    }
}

@media only screen and (max-width: 767px) {
    .services-details-desc .content-image .sub-title {
        left: 25px;
        font-size: 18px;
    }
}

.two-line-service-title a {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3; /* Adjust based on your design */
    max-height: calc(2 * 1.3em); /* 2 lines × line-height */
}

.top-header-area.bg-color{
    background-color: black;
}

.top-header-area.bg-color .top-header-information li {
    color: #fff;
}

.top-header-area.bg-color .top-header-information li i {
    color: #fff;
}

.top-header-area.bg-color .top-header-information li a {
    color: #fff;
}

.top-header-area.bg-color .top-header-optional li a i {
    color: #fff;
}

.main-navbar {
    padding: 10px 0;
    background: #fff;
    box-shadow: 0 0 1.25rem rgb(108 123 148 / 10%);
}

/* Logo Sizing */
.navbar-brand img,
.logo img {
    max-width: 200px !important;
    height: auto !important;
}

/* Responsive logo sizing */
@media (max-width: 768px) {
    .navbar-brand img,
    .logo img {
        max-width: 120px !important;
    }
}

/* Language Dropdown Button Styling */
.dropdown .btn-secondary {
    background-color: #000000 !important;
    border-color: #000000 !important;
    color: #ffffff !important;
}

.dropdown .btn-secondary:hover,
.dropdown .btn-secondary:focus,
.dropdown .btn-secondary:active {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

/* Footer Navigation Links Hover */
.single-footer-widget .quick-links li a {
    color: #ffffff !important;
    transition: all 0.6s ease !important;
}

.single-footer-widget .quick-links li a:hover {
    color: #6c757d !important;
    transform: translateX(5px) !important;
}

.single-footer-widget .footer-contact-info li a {
    color: #6c757d !important;
    transition: all 0.6s ease !important;
}

.single-footer-widget .footer-contact-info li a:hover {
    color: #ffffff !important;
}

/* Footer Copyright Link Hover */
.copyright-area .copyright-area-content p a {
    color: #6c757d !important;
    transition: all 0.6s ease !important;
}

.copyright-area .copyright-area-content p a:hover {
    color: #ffffff !important;
}

/* About Section - More About Us Button */
.about-btn .default-btn {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.about-btn .default-btn:hover {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.about-btn .default-btn i {
    background-color: #ffffff !important;
    color: #000000 !important;
}

.about-btn .default-btn:hover i {
    background-color: #ffffff !important;
    color: #000000 !important;
}

/* Services Section - Category Buttons */
.services-btn .default-btn {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.services-btn .default-btn:hover {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.services-btn .default-btn i {
    background-color: #ffffff !important;
    color: #000000 !important;
}

.services-btn .default-btn:hover i {
    background-color: #ffffff !important;
    color: #000000 !important;
}

/* Blog Section - Read More Buttons */
.blog-btn .default-btn {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.blog-btn .default-btn:hover {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.blog-btn .default-btn i {
    background-color: #ffffff !important;
    color: #000000 !important;
}

.blog-btn .default-btn:hover i {
    background-color: #ffffff !important;
    color: #000000 !important;
}

/* Fix blog item hover background */
.single-blog-item:hover .blog-content {
    background-color: #6c757d !important;
}

.single-blog:hover .blog-content {
    background-color: #6c757d !important;
}

.main-navbar .navbar .navbar-nav .nav-item a:hover, .main-navbar .navbar .navbar-nav .nav-item a:focus, .main-navbar .navbar .navbar-nav .nav-item a.active {
    color: #ff6f6f;
}