@extends('public.layouts.index')

@push('meta')
    <title>{{ getTranslation($blog, 'title') }} | {{ config('app.name') }}</title>
    <meta name="description" content="{{ Str::limit(strip_tags(getTranslation($blog, 'description')), 160) }}">
    <meta property="og:title" content="{{ getTranslation($blog, 'title') }} | {{ config('app.name') }}">
    <meta property="og:description" content="{{ Str::limit(strip_tags(getTranslation($blog, 'description')), 160) }}">
    <meta property="og:image" content="{{ $blog->cover }}">
    <meta property="og:url" content="{{ route('blog.show', $blog->slug) }}">
    <meta property="og:type" content="article">
    <meta property="article:author" content="{{ $blog->author }}">
    <meta property="article:published_time" content="{{ $blog->published_at }}">
    <meta name="twitter:title" content="{{ getTranslation($blog, 'title') }} | {{ config('app.name') }}">
    <meta name="twitter:description" content="{{ Str::limit(strip_tags(getTranslation($blog, 'description')), 160) }}">
    <meta name="twitter:image" content="{{ $blog->cover }}">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="keywords" content="HR blog, {{ getTranslation($blog, 'title') }}, HelloNatorie, human resources">
    <meta name="author" content="{{ $blog->author }}">
@endpush

@section('content')
    <div class="page-banner-area ptb-100 pt-288">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-12 col-md-12">
                    <div class="page-banner-content">
                        <h2>{{ getTranslation($blog, 'title') }}</h2>
                        <ul>
                            <li>
                                <a href="/">{{ __('translation.Home') }}</a>
                            </li>
                            <li>{{ getTranslation($blog, 'title') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-banner-shape">
            <img src="/assets/images/page-banner/banner-shape-1.png" alt="image">
        </div>
    </div>

    <div class="blog-details-area ptb-100">
        <div class="container">
            <div class="blog-details-desc">
                <div class="article-content">
                    <div class="title-box">
                        <h2>{{ getTranslation($blog, 'title') }}</h2>
                        <div class="entry-meta">
                            <ul>
                                <li><i class="flaticon-calendar"></i> <a href="#">{{ \Carbon\Carbon::parse($blog->published_at)->format('M d, Y') }}</a></li>
                                <li><i class="flaticon-user"></i> <a href="#">{{ $blog->author }}</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="article-image text-center">
                        <img src="{{ $blog->cover_landscape }}" alt="image">
                    </div>

                    <div class="article-content mt-5">
                        {{ getTranslation($blog, 'description') }}
                    </div>

                    <div class="article-content mt-5">
                        {!! getTranslation($blog, 'content') !!}
                    </div>
                </div>

                <div class="article-footer col-12 d-block" style="justify-items: flex-end">
                    <div class="article-share col-12">
                        <span>{{ __('translation.Share') }}:</span>

                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}" target="_blank">
                            <i class="bx bxl-facebook"></i>
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ url()->current() }}" target="_blank">
                            <i class="bx bxl-twitter"></i>
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ url()->current() }}" target="_blank">
                            <i class="bx bxl-linkedin"></i>
                        </a>
                    </div>
                </div>

                <div class="psylo-post-navigation">
                    <div class="prev-link-wrapper">
                        <div class="info-prev-link-wrapper">
                            @if ($previous)
                                <a href="{{ route('blog.show', $previous->slug) }}">
                                    <span class="image-prev">
                                        <img src="{{ $previous->cover }}" alt="image">
                                        <span class="post-nav-title">{{ __('translation.Prev') }}</span>
                                    </span>

                                    <span class="prev-link-info-wrapper">
                                        <span class="prev-title">
                                            {{ getTranslation($previous, 'title') }}
                                        </span>
                                    </span>
                                </a>
                            @endif
                        </div>
                    </div>

                    <div class="next-link-wrapper">
                        <div class="info-next-link-wrapper">
                            @if ($next)
                                <a href="{{ route('blog.show', $next->slug) }}">
                                    <span class="next-link-info-wrapper">
                                        <span class="next-title">
                                            {{ getTranslation($next, 'title') }}
                                        </span>
                                    </span>

                                    <span class="image-next">
                                        <img src="{{ $next->cover }}" alt="image">
                                        <span class="post-nav-title">{{ __('translation.Next') }}</span>
                                    </span>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
