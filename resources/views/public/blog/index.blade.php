@extends('public.layouts.index')

@push('meta')
    <title>{{ __('translation.Blogs') }} | {{ config('app.name') }}</title>
    <meta name="description" content="{{ __('translation.Blog Meta Description') }}">
    <meta property="og:title" content="{{ __('translation.Blogs') }} | {{ config('app.name') }}">
    <meta property="og:description" content="{{ __('translation.Blog Meta Description') }}">
    <meta property="og:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta property="og:url" content="{{ url('/blogs') }}">
    <meta property="og:type" content="website">
    <meta name="twitter:title" content="{{ __('translation.Blogs') }} | {{ config('app.name') }}">
    <meta name="twitter:description" content="{{ __('translation.Blog Meta Description') }}">
    <meta name="twitter:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="keywords" content="HR blog, human resources insights, business tips, employee management, HelloNatorie blog">
    <meta name="author" content="HelloNatorie">
@endpush

@section('content')
    <div class="page-banner-area ptb-100 pt-288">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-12 col-md-12">
                    <div class="page-banner-content">
                        <h2>{{ __('translation.Blogs') }}</h2>
                        <ul>
                            <li>
                                <a href="/">{{ __('translation.Home') }}</a>
                            </li>
                            <li>{{ __('translation.Blogs') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-banner-shape">
            <img src="/assets/images/page-banner/banner-shape-1.png" alt="image">
        </div>
    </div>

    <section class="blog-area pt-100 pb-100">
        <div class="container">
            <div class="row">
                @foreach ($blogs as $blog)
                    <div class="col-lg-6 col-md-6">
                        <div class="single-blog-item">
                            <div class="blog-image">
                                <a href="{{ route('blog.show', $blog->slug) }}"><img src="{{ $blog->cover }}" alt="image"></a>

                                <div class="tag">{{ \Carbon\Carbon::parse($blog->created_at)->format('d') }} <span>{{ \Carbon\Carbon::parse($blog->created_at)->format('M') }}</span></div>
                            </div>

                            <div class="blog-content">
                                <div class="meta">
                                    <p>
                                        <i class="flaticon-user"></i>
                                        {{ __('translation.By') }} <a href="#">{{ $blog->author }}</a>
                                    </p>
                                </div>

                                <h3>
                                    <a href="{{ route('blog.show', $blog->slug) }}">{{ getTranslation($blog, 'title') }}</a>
                                </h3>
                                <div class="blog-btn">
                                    <a href="{{ route('blog.show', $blog->slug) }}" class="default-btn">{{ __('translation.Read More') }} <i class="flaticon-plus"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach


                {{-- <div class="col-lg-12 col-md-12">
                    <div class="pagination-area">
                        <a href="#" class="prev page-numbers"><i class="bx bx-chevrons-left"></i></a>
                        <span class="page-numbers current" aria-current="page">1</span>
                        <a href="#" class="page-numbers">2</a>
                        <a href="#" class="page-numbers">3</a>
                        <a href="#" class="page-numbers">4</a>
                        <a href="#" class="next page-numbers"><i class="bx bx-chevrons-right"></i></a>
                    </div>
                </div> --}}
            </div>
        </div>
    </section>
@endsection
