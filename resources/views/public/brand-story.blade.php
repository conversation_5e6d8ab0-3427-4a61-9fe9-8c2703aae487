@extends('public.layouts.index')

@push('meta')
    <title>{{ __('translation.Brand Story') }} | {{ config('app.name') }}</title>
    <meta name="description" content="{{ __('translation.Brand Story Meta Description') }}">
    <meta property="og:title" content="{{ __('translation.Brand Story') }} | {{ config('app.name') }}">
    <meta property="og:description" content="{{ __('translation.Brand Story Meta Description') }}">
    <meta property="og:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta property="og:url" content="{{ url('/brand-story') }}">
    <meta property="og:type" content="website">
    <meta name="twitter:title" content="{{ __('translation.Brand Story') }} | {{ config('app.name') }}">
    <meta name="twitter:description" content="{{ __('translation.Brand Story Meta Description') }}">
    <meta name="twitter:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="keywords" content="HelloNatorie story, company history, HR consulting journey, business story">
    <meta name="author" content="HelloNatorie">
@endpush

@section('content')
    <div class="page-banner-area pt-140 pt-288">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-12 col-md-12">
                    <div class="page-banner-content">
                        <h2>{{ __('translation.Brand Story') }}</h2>
                        <ul>
                            <li>
                                <a href="/">{{ __('translation.Home') }}</a>
                            </li>
                            <li>{{ __('translation.Brand Story') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-banner-shape">
            <img src="/assets/images/page-banner/banner-shape-1.png" alt="image">
        </div>
    </div>

    <section class="brand-story-modern-area pt-100 pb-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-11 col-xl-10">
                    <div class="brand-story-modern-card">
                        <div class="row align-items-start">
                            <div class="col-lg-6">
                                <div class="philosophy-image">
                                    <img src="/assets/images/philosophy/philosophy-1.png" alt="image">
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="brand-story-content-section">
                                    <h2 class="story-title">{{ __('translation.Why I Started Natorie?') }}</h2>

                                    <div class="story-content">
                                        <p class="story-text">
                                            {{ __('translation.Brand Story Paragraph 1') }}
                                        </p>

                                        <p class="story-text">
                                            {{ __('translation.Brand Story Paragraph 2') }}
                                        </p>

                                        <div class="story-highlight">
                                            <p class="highlight-text">{{ __('translation.Brand Story Question') }}</p>
                                        </div>

                                        <p class="story-text">
                                            {{ __('translation.Brand Story Paragraph 3') }}
                                        </p>

                                        <p class="story-text">
                                            {{ __('translation.Brand Story Paragraph 4') }}
                                        </p>

                                        <div class="story-points">
                                            <h4 class="points-title">{{ __('translation.What We Believe') }}:</h4>
                                            <ul class="belief-points">
                                                <li>{{ __('translation.Belief Point 1') }}</li>
                                                <li>{{ __('translation.Belief Point 2') }}</li>
                                                <li>{{ __('translation.Belief Point 3') }}</li>
                                            </ul>
                                        </div>

                                        <p class="story-conclusion">
                                            {{ __('translation.Brand Story Conclusion') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Modern Brand Story Styles */
        .brand-story-modern-area {
            background-color: #000000;
            padding: 100px 0;
            min-height: 100vh;
        }

        .brand-story-modern-card {
            background: #ffffff;
            border-radius: 30px;
            padding: 50px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .philosophy-image {
            position: relative;
            padding-right: 30px;
            align-self: flex-start;
        }

        .philosophy-image img {
            width: 100%;
            height: auto;
            display: block;
            border-radius: 20px;
        }

        .brand-logo-badge {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 20px;
            border-radius: 12px;
            z-index: 2;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .brand-text {
            font-size: 16px;
            font-weight: 700;
            color: #000000;
            letter-spacing: 3px;
            font-family: 'Poppins', sans-serif;
        }

        .brand-story-content-section {
            padding-left: 30px;
        }

        .story-title {
            font-size: 36px;
            font-weight: 700;
            color: #000000;
            margin-bottom: 30px;
            line-height: 1.2;
            font-family: 'Poppins', sans-serif;
        }

        .story-content {
            color: #333333;
        }

        .story-text {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 20px;
            color: #444444;
            text-align: justify;
        }

        .story-highlight {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 4px solid #000000;
        }

        .highlight-text {
            font-size: 17px;
            font-weight: 600;
            color: #000000;
            margin: 0;
            font-style: italic;
        }

        .story-points {
            margin: 30px 0;
        }

        .points-title {
            font-size: 20px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 15px;
        }

        .belief-points {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .belief-points li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 12px;
            font-size: 16px;
            color: #444444;
            line-height: 1.6;
        }

        .belief-points li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #000000;
            font-weight: bold;
            font-size: 18px;
        }

        .story-conclusion {
            font-size: 17px;
            font-weight: 600;
            color: #000000;
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            text-align: center;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .philosophy-image {
                padding-right: 0;
                margin-bottom: 40px;
            }

            .brand-story-content-section {
                padding-left: 0;
            }
        }

        @media (max-width: 768px) {
            .brand-story-modern-card {
                padding: 30px 25px;
                border-radius: 20px;
            }

            .story-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            .story-text {
                font-size: 15px;
            }

            .brand-logo-badge {
                top: 15px;
                left: 15px;
                padding: 10px 16px;
            }

            .brand-text {
                font-size: 14px;
                letter-spacing: 2px;
            }
        }

        @media (max-width: 576px) {
            .brand-story-modern-area {
                padding: 60px 0;
            }

            .brand-story-modern-card {
                padding: 25px 20px;
                margin: 0 10px;
            }

            .story-title {
                font-size: 24px;
            }

            .story-highlight {
                padding: 15px;
            }

            .highlight-text {
                font-size: 16px;
            }
        }
    </style>
@endsection
