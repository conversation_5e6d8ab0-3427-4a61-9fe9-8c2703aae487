@extends('public.layouts.index')

@push('meta')
    <title>{{ __('translation.Brand Story') }} | {{ config('app.name') }}</title>
@endpush

@section('content')
    <div class="page-banner-area pt-140 pt-288">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-12 col-md-12">
                    <div class="page-banner-content">
                        <h2>{{ __('translation.Brand Story') }}</h2>
                        <ul>
                            <li>
                                <a href="/">{{ __('translation.Home') }}</a>
                            </li>
                            <li>{{ __('translation.Brand Story') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-banner-shape">
            <img src="/assets/images/page-banner/banner-shape-1.png" alt="image">
        </div>
    </div>

    <section class="brand-story-area bg-ffffff pt-140 pb-100">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-10">
                    <div class="brand-story-card">
                        <div class="row align-items-center">
                            <div class="col-lg-6">
                                <div class="philosophy-image">
                                    <img src="/assets/images/philosophy/philosophy-1.png" alt="image">
                                </div>
                            </div>
                            
                            <div class="col-lg-6 col-md-6">
                                <div class="brand-story-content">
                                    <h3>{{ __('translation.Why I Started Natorie?') }}</h3>
                                    
                                    <p class="story-paragraph">
                                        {{ __('translation.Brand Story Paragraph 1') }}
                                    </p>
                                    
                                    <p class="story-paragraph">
                                        {{ __('translation.Brand Story Paragraph 2') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .brand-story-area {
            padding-top: 140px;
            padding-bottom: 100px;
        }
        
        .brand-story-card {
            background: #ffffff;
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 0 20px;
        }
        
        .brand-story-image {
            position: relative;
            margin-bottom: 30px;
        }
        
        .brand-story-image img {
            width: 100%;
            height: auto;
            border-radius: 15px;
        }
        
        .brand-logo-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 8px;
        }
        
        .brand-name {
            font-size: 18px;
            font-weight: 700;
            color: #000000;
            margin: 0;
            letter-spacing: 2px;
        }
        
        .brand-story-content h3 {
            font-size: 32px;
            font-weight: 700;
            color: #000000;
            margin-bottom: 30px;
            line-height: 1.3;
        }
        
        .story-paragraph {
            font-size: 16px;
            line-height: 1.8;
            color: #333333;
            margin-bottom: 25px;
            text-align: justify;
        }
        
        .story-paragraph:last-child {
            margin-bottom: 0;
        }
        
        @media (max-width: 768px) {
            .brand-story-card {
                padding: 40px 20px;
                margin: 0 10px;
            }
            
            .brand-story-content h3 {
                font-size: 24px;
                margin-bottom: 20px;
            }
            
            .story-paragraph {
                font-size: 15px;
                margin-bottom: 20px;
            }
            
            .brand-story-image {
                margin-bottom: 40px;
            }
        }
        
        @media (max-width: 576px) {
            .brand-story-area {
                padding-top: 80px;
                padding-bottom: 60px;
            }
            
            .brand-story-card {
                padding: 30px 15px;
                border-radius: 15px;
            }
            
            .brand-story-content h3 {
                font-size: 22px;
            }
        }
    </style>
@endsection
