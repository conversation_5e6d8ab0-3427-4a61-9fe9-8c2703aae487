@extends('public.layouts.index')

@push('meta')
    <title>{{ __('translation.Contact') }} | {{ config('app.name') }}</title>
    <meta name="description" content="{{ __('translation.Contact Meta Description') }}">
    <meta property="og:title" content="{{ __('translation.Contact') }} | {{ config('app.name') }}">
    <meta property="og:description" content="{{ __('translation.Contact Meta Description') }}">
    <meta property="og:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta property="og:url" content="{{ url('/contact') }}">
    <meta property="og:type" content="website">
    <meta name="twitter:title" content="{{ __('translation.Contact') }} | {{ config('app.name') }}">
    <meta name="twitter:description" content="{{ __('translation.Contact Meta Description') }}">
    <meta name="twitter:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="keywords" content="contact HelloNatorie, HR consulting contact, get in touch, business consultation">
    <meta name="author" content="HelloNatorie">
@endpush

@section('content')
    <div class="page-banner-area ptb-100 pt-288">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-12 col-md-12">
                    <div class="page-banner-content">
                        <h2>{{ __('translation.Contact') }}</h2>
                        <ul>
                            <li>
                                <a href="/">{{ __('translation.Home') }}</a>
                            </li>
                            <li>{{ __('translation.Contact') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-banner-shape">
            <img src="/assets/images/page-banner/banner-shape-1.png" alt="image">
        </div>
    </div>

    <section class="contact-info-area pt-100 pb-70">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-4 col-md-6 col-sm-6">
                    <div class="contact-info-box">
                        <div class="icon">
                            <i class="flaticon-phone-call"></i>
                            <h3>{{ __('translation.Phone Number') }}</h3>
                        </div>

                        <p><i class="flaticon-check"></i> <a href="tel:009832436231">+00 983 2436 231</a></p>
                        <p><i class="flaticon-check"></i> <a href="tel:009832436232">+00 983 2436 232</a></p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 col-sm-6">
                    <div class="contact-info-box">
                        <div class="icon">
                            <i class="flaticon-mail"></i>
                            <h3>{{ __('translation.Email Address') }}</h3>
                        </div>

                        <p><i class="flaticon-check"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><i class="flaticon-check"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 col-sm-6">
                    <div class="contact-info-box">
                        <div class="icon">
                            <i class="flaticon-placeholder"></i>
                            <h3>{{ __('translation.Address') }}</h3>
                        </div>

                        <p><i class="flaticon-check"></i> 175 5th Ave Premium Area, New York, NY 10010, United States</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="contact-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="contact-form">
                        <h3>{{ __('translation.Get In') }} <span>{{ __('translation.Touch') }}</span></h3>

                        <form id="contactForm" novalidate="true">
                            <div class="form-group">
                                <input type="text" name="name" id="name" class="form-control" required="" data-error="{{ __('translation.Please enter your name') }}" placeholder="{{ __('translation.Name') }}">
                                <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-group">
                                <input type="email" name="email" id="email" class="form-control" required="" data-error="{{ __('translation.Please enter your email') }}" placeholder="{{ __('translation.Email') }}">
                                <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-group">
                                <input type="text" name="phone_number" id="phone_number" required="" data-error="{{ __('translation.Please enter your number') }}" class="form-control" placeholder="{{ __('translation.Phone') }}">
                                <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-group">
                                <input type="text" name="msg_subject" id="msg_subject" class="form-control" required="" data-error="{{ __('translation.Please enter your subject') }}" placeholder="{{ __('translation.Subject') }}">
                                <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-group">
                                <textarea name="message" class="form-control" id="message" cols="30" rows="5" required="" data-error="{{ __('translation.Write your message') }}" placeholder="{{ __('translation.Your Message') }}"></textarea>
                                <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="accept">
                                <label class="form-check-label" for="accept">{{ __('translation.I Accept All') }} <a href="/terms-of-service">{{ __('translation.Terms & Condition') }}</a></label>
                            </div>

                            <button type="submit" class="default-btn disabled" style="pointer-events: all; cursor: pointer;">{{ __('translation.Send Message') }} <i class="flaticon-pointer"></i></button>
                            <div id="msgSubmit" class="h3 text-center hidden"></div>
                            <div class="clearfix"></div>
                        </form>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="contact-image"></div>
                </div>
            </div>
        </div>
    </section>
@endsection
