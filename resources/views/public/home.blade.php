@extends('public.layouts.index')

@push('meta')
    <title>{{ config('app.name') }} | {{ __('translation.Home') }}</title>
    <meta name="description" content="{{ __('translation.Home Meta Description') }}">
    <meta property="og:title" content="{{ config('app.name') }} | {{ __('translation.Home') }}">
    <meta property="og:description" content="{{ __('translation.Home Meta Description') }}">
    <meta property="og:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta property="og:url" content="{{ route('home') }}">
    <meta property="og:type" content="website">
    <meta name="twitter:title" content="{{ config('app.name') }} | {{ __('translation.Home') }}">
    <meta name="twitter:description" content="{{ __('translation.Home Meta Description') }}">
    <meta name="twitter:image" content="/assets/images/hellonatorie-logo-white.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="keywords" content="HR consulting, human resources, talent acquisition, employee engagement, HelloNatorie">
    <meta name="author" content="HelloNatorie">
@endpush

@section('content')
    @if ($banners->count() > 0)
        <div class="main-slides-area">
            <div class="home-slides owl-carousel owl-theme owl-loaded owl-drag">
                <div class="owl-stage-outer">
                    <div class="owl-stage">
                        @foreach ($banners as $banner)
                            <div class="owl-item">
                                <div class="main-slides-item item-two" style="background-image: url({{ $banner->cover }});">
                                    <div class="container">
                                        <div class="main-slides-content">
                                            <h1>{{ getTranslation($banner, 'title') }}</h1>
                                            <p>{{ getTranslation($banner, 'description') }}</p>

                                            @if (getTranslation($banner, 'button_text'))
                                                <div class="slides-btn">
                                                    @if ($banner->external_url)
                                                        <a href="{{ $banner->external_url }}" class="default-btn" target="_blank">{{ getTranslation($banner, 'button_text') }} <i class="flaticon-pointer"></i></a>
                                                    @else
                                                        <a href="{{ route('banner.show', $banner->slug) }}" class="default-btn">{{ getTranslation($banner, 'button_text') }} <i class="flaticon-pointer"></i></a>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="owl-nav disabled">
                    <button type="button" role="presentation" class="owl-prev">
                        <i class="flaticon-left-arrow"></i></button>
                    <button type="button" role="presentation" class="owl-next">
                        <i class="flaticon-right-arrow"></i>
                    </button>
                </div>
                <div class="owl-dots">

                </div>
            </div>
        </div>
    @endif

    <section class="about-area bg-ffffff pt-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 order-md-0 order-1 mt-md-0 mt-5">
                    <div class="about-main-content">
                        <h3>{{ __('translation.Know About Us') }}</h3>

                        <div class="about-content-image">
                            <img src="/assets/images/about/about-2.jpg" alt="image">

                            <h4 class="sub-title">HelloNatorie</h4>

                            <a href="https://www.youtube.com/watch?v=ODfy2YIKS1M" class="video-btn popup-youtube">
                                <i class="flaticon-play"></i>
                            </a>
                        </div>
                        <p>{{ __('translation.Welcome Message') }}</p>

                        <div class="row">
                            <div class="col-lg-6 col-sm-6">
                                <div class="about-information">
                                    <i class="flaticon-trophy"></i>
                                    <h5>{{ __('translation.10+ Years') }}</h5>
                                    <span>{{ __('translation.Experience') }}</span>
                                </div>
                            </div>

                            <div class="col-lg-6 col-sm-6">
                                <div class="about-information">
                                    <i class="flaticon-customer"></i>
                                    <h5>{{ __('translation.10K+ Happy') }}</h5>
                                    <span>{{ __('translation.Clients') }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="about-btn">
                            <a href="/about-us" class="default-btn">{{ __('translation.More About Us') }} <i class="flaticon-user"></i></a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 order-0 order-md-0">
                    <div class="about-main-image">
                        <img src="assets/images/about/about-1.jpg" alt="image">

                        <div class="about-shape about-wrap">
                            <div class="shape-1">
                                <img src="assets/images/about/shape-1.png" alt="image">
                            </div>

                            <div class="shape-2">
                                <img src="assets/images/about/shape-2.png" alt="image">
                            </div>

                            <div class="shape-3">
                                <img src="assets/images/about/shape-3.png" alt="image">
                            </div>

                            <div class="shape-4">
                                <img src="assets/images/about/shape-4.png" alt="image">
                            </div>

                            <div class="shape-5">
                                <img src="assets/images/about/shape-5.png" alt="image">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if ($categories->count() > 0)
        <div class="services-area pt-5">
            <div class="container">
                <div class="section-title">
                    <h2>{{ __('translation.What') }} <span>{{ __('translation.Services') }}</span> {{ __('translation.We Provide') }}</h2>
                    <p>{{ __('translation.HR Solutions Description') }}</p>
                </div>

                <div class="tab services-list-tab">
                    <ul class="tabs active">
                        @foreach ($categories as $key => $category)
                            <li class="{{ $key == 0 ? 'current' : '' }}">
                                <a href="#" style="padding: 25px 15px 25px 15px;">
                                    {{ getTranslation($category, 'name') }}
                                </a>
                            </li>
                        @endforeach
                    </ul>

                    <div class="tab_content">
                        @foreach ($categories as $key => $category)
                            <div class="tabs_item">
                                <div class="row align-items-center">
                                    <div class="col-lg-6">
                                        <div class="services-tab-image">
                                            <img src="{{ $category->cover }}" alt="image">

                                            <div class="services-tab-shape">
                                                <div class="shape-1">
                                                    <img src="/assets/images/services/shape-1.png" alt="image">
                                                </div>

                                                <div class="shape-2">
                                                    <img src="/assets/images/services/shape-2.png" alt="image">
                                                </div>

                                                <div class="shape-3">
                                                    <img src="/assets/images/services/shape-3.png" alt="image">
                                                </div>

                                                <div class="shape-4">
                                                    <img src="/assets/images/services/shape-4.png" alt="image">
                                                </div>

                                                <div class="circle-shape">
                                                    <img src="/assets/images/services/circle-shape.png" alt="image">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="services-tab-content">
                                            <div class="services-content-image">
                                                <img src="/assets/images/services/flower.jpg" alt="image">

                                                <h4 class="sub-title">{{ getTranslation($category, 'name') }}</h4>
                                            </div>
                                            <p>{{ getTranslation($category, 'description') }}</p>

                                            <div class="services-btn">
                                                @foreach ($category->services as $group => $services)
                                                    @php
                                                        // Get the first service in the group to display its translated name
                                                        $firstService = $services->first();
                                                    @endphp
                                                    <a href="{{ route('service.select-company', [$category, $group]) }}" class="default-btn mb-2">{{ getTranslation($firstService->group, 'name') }} <i class="flaticon-plus"></i></a>
                                                @endforeach
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if ($highlights->count() > 0)
        <section class="reviews-area pt-140">
            <div class="container">
                <div class="clients-slides owl-carousel owl-theme owl-loaded owl-drag">
                    <div class="owl-stage-outer">
                        <div class="owl-stage" style="transform: translate3d(-2898px, 0px, 0px); transition: 0.5s; width: 6762px;">
                            @foreach ($highlights as $highlight)
                                <div class="owl-item" style="width: 936px; margin-right: 30px;">
                                    <div class="clients-item">
                                        <div class="row align-items-center">
                                            <div class="col-lg-6">
                                                <div class="reviews-image">
                                                    <img src="{{ $highlight->cover }}" alt="image">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="item">
                                                    <div class="title">
                                                        <h3>{{ __('translation.Highlight') }}</h3>
                                                    </div>

                                                    <div class="single-feedback">
                                                        <div class="icon">
                                                            <i class="flaticon-close"></i>
                                                        </div>
                                                        <p>{{ getTranslation($highlight, 'description') }}</p>
                                                    </div>

                                                    <div class="title-info mt-5">
                                                        <h3>{{ getTranslation($highlight, 'title') }}</h3>
                                                    </div>

                                                    @if ($highlight->button_text)
                                                        <div class="slides-btn mt-5">
                                                            @if ($highlight->external_url)
                                                                <a href="{{ $highlight->external_url }}" class="default-btn" target="_blank">{{ getTranslation($highlight, 'button_text') }} <i class="flaticon-pointer"></i></a>
                                                            @else
                                                                <a href="{{ route('highlight.show', $highlight->slug) }}" class="default-btn">{{ getTranslation($highlight, 'button_text') }} <i class="flaticon-pointer"></i></a>
                                                            @endif
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <div class="owl-nav"><button type="button" role="presentation" class="owl-prev"><i class="bx bxs-left-arrow"></i></button><button type="button" role="presentation" class="owl-next"><i class="bx bxs-right-arrow"></i></button></div>
                    <div class="owl-dots disabled"></div>
                </div>
            </div>

            <div class="reviews-main-shape">
                <img src="/assets/images/reviews/shape-1.png" alt="image">
            </div>
        </section>
    @endif
    <section class="fun-facts-area bg-top pt-100 pb-70">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="single-fun-fact">
                        <div class="icon">
                            <i class="flaticon-trophy"></i>
                        </div>
                        <h3>
                            <span class="odometer odometer-auto-theme" data-count="25">
                                <div class="odometer-inside"><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span class="odometer-value">2</span></span></span></span></span><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span
                                                        class="odometer-value">5</span></span></span></span></span></div>
                            </span>
                            <span class="sign-icon"><b>+</b> {{ __('translation.Years') }}</span>
                        </h3>
                        <p>{{ __('translation.Experience') }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="single-fun-fact">
                        <div class="icon">
                            <i class="flaticon-customer"></i>
                        </div>
                        <h3>
                            <span class="odometer odometer-auto-theme" data-count="100">
                                <div class="odometer-inside"><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span class="odometer-value">1</span></span></span></span></span><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span
                                                        class="odometer-value">0</span></span></span></span></span><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span class="odometer-value">0</span></span></span></span></span></div>
                            </span>
                            <span class="sign-icon"><b>+</b> {{ __('translation.Happy') }}</span>
                        </h3>
                        <p>{{ __('translation.Clients') }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="single-fun-fact">
                        <div class="icon">
                            <i class="flaticon-user"></i>
                        </div>
                        <h3>
                            <span class="odometer odometer-auto-theme" data-count="50">
                                <div class="odometer-inside"><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span class="odometer-value">5</span></span></span></span></span><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span
                                                        class="odometer-value">0</span></span></span></span></span></div>
                            </span>
                            <span class="sign-icon"><b>+</b> {{ __('translation.Best') }}</span>
                        </h3>
                        <p>{{ __('translation.Consultants') }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="single-fun-fact">
                        <div class="icon">
                            <i class="flaticon-medal"></i>
                        </div>
                        <h3>
                            <span class="odometer odometer-auto-theme" data-count="23">
                                <div class="odometer-inside"><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span class="odometer-value">2</span></span></span></span></span><span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span
                                                        class="odometer-value">3</span></span></span></span></span></div>
                            </span>
                            <span class="sign-icon"><b>+</b> {{ __('translation.Winning') }}</span>
                        </h3>
                        <p>{{ __('translation.Awards') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if ($blogs->count() > 0)
        <section class="blog-area pt-100 pb-70">
            <div class="container">
                <div class="section-title">
                    <h2>{{ __('translation.Dive Into Our Blog') }}</h2>
                    <p>{{ __('translation.Blog Description') }}</p>
                </div>

                <div class="blog-slides owl-carousel owl-theme owl-loaded owl-drag">
                    <div class="owl-stage-outer">
                        <div class="owl-stage" style="transform: translate3d(-1449px, 0px, 0px); transition: 0.5s; width: 3864px;">
                            @foreach ($blogs as $blog)
                                <div class="owl-item" style="width: 453px; margin-right: 30px;">
                                    <div class="single-blog-item">
                                        <div class="blog-image">
                                            <a href="{{ route('blog.show', $blog->slug) }}"><img src="{{ $blog->cover }}" alt="image"></a>

                                            <div class="tag">{{ \Carbon\Carbon::parse($blog->created_at)->format('d') }} <span>{{ \Carbon\Carbon::parse($blog->created_at)->format('M') }}</span></div>
                                        </div>

                                        <div class="blog-content">
                                            <div class="meta">
                                                <p>
                                                    <i class="flaticon-user"></i>
                                                    {{ __('translation.By') }} <a href="#">{{ $blog->author }}</a>
                                                </p>
                                            </div>

                                            <h3>
                                                <a href="{{ route('blog.show', $blog->slug) }}">{{ getTranslation($blog, 'title') }}</a>
                                            </h3>
                                            <div class="blog-btn">
                                                <a href="{{ route('blog.show', $blog->slug) }}" class="default-btn">{{ __('translation.Read More') }} <i class="flaticon-plus"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <div class="owl-nav"><button type="button" role="presentation" class="owl-prev"><i class="flaticon-left-arrow"></i></button><button type="button" role="presentation" class="owl-next"><i class="flaticon-right-arrow"></i></button></div>
                    <div class="owl-dots disabled"></div>
                </div>
            </div>
        </section>
    @endif
@endsection
